﻿@using System.Diagnostics
@inherits RadzenComponent


@if (Visible)
{
    <div @ref=Element id="@GetId()" @attributes="Attributes" class="@GetComponentCssClass()" style="flex-basis: @Size; @Style" data-index="@Index">
        @ChildContent
        @if (Splitter.IsResizing)
        {
            <div class="rz-splitter-mask" />
        }
    </div>


    @if (!IsLast && BarVisible)
    {
        <div class="@GetComponentBarCssClass()"
             @onclick:preventDefault="true"
             @onclick:stopPropagation="true"
             @onpointerdown=@(args => Splitter.StartResize(args, Index))>

            @if (IsCollapsible)
            {
                <span tabindex="0" class="rz-collapse" id="@(GetId() + "-collapse")"
                    @onkeypress="@(args => OnKeyPress(args, false))" @onkeypress:preventDefault=preventKeyPress @onkeypress:stopPropagation
                    @onmousedown:preventDefault="true" @onmousedown:stopPropagation="true" @onmousedown=@(args => Splitter.OnCollapse(Index))>
                </span>
            }

            @if (IsResizable)
            {
                <span tabindex="0" class="rz-resize" id="@(GetId() + "-resize")"
                    @onkeydown="@(args => OnKeyPress(args))" @onkeydown:preventDefault=preventKeyPress @onkeydown:stopPropagation>
                </span>
            }

            @if (IsExpandable)
            {
                <span tabindex="0" class="rz-expand" id="@(GetId() + "-expand")"
                    @onkeypress="@(args => OnKeyPress(args, true))" @onkeypress:preventDefault=preventKeyPress @onkeypress:stopPropagation
                    @onmousedown:preventDefault="true" @onmousedown:stopPropagation="true" @onmousedown=@(args => Splitter.OnExpand(Index))>
                </span>
            }


        </div>
    }
}
