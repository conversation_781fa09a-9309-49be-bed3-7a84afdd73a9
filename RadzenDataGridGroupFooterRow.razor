@typeparam TItem
@for (var i = 0; i < Grid.deepestChildColumnLevel + 1; i++)
{
    <tr>
        @if (i == 0) // Only add the th elements for the first row
        {
            @if (Grid.Template != null && Grid.ShowExpandColumn)
            {
                @if (Grid.ShowGroupExpandColumn)
                {
                    <th class="rz-col-icon rz-unselectable-text" scope="col">
                        <span class="rz-column-title"></span>
                    </th>
                }

            }
        }
        @for (var j = 0; j < GetLevel(); j++)
        {
            <td class="rz-col-icon">
                <span class="rz-column-title"></span>
            </td>
        }
        @foreach (var column in Columns)
        {
            <RadzenDataGridGroupFooterCell Group="@Group" RowIndex="@i" Grid="@Grid" Column="@column" CssClass="@($"{column.GroupFooterCssClass} {Grid.getFrozenColumnClass(column, Columns)} {Grid.getCompositeCellCSSClass(column)}".Trim())" />
        }
    </tr>
}
@code {
        [Parameter]
        public IList<RadzenDataGridColumn<TItem>> Columns { get; set; }

    GroupResult _groupResult;
        [Parameter]
    public GroupResult GroupResult
        {
            get
            {
                return _groupResult;
            }
            set
            {
                if(_groupResult != value)
                {
                    _groupResult = value;

                    var l = GetLevel();
                    var level = l > 0 ? l - 1 : l;

                    Group = new Group()
                    {
                        Level = level,
                        GroupDescriptor = Grid.Groups[level],
                        Data = _groupResult
                    };
                }
            }
        }

        [Parameter]
        public RadzenDataGrid<TItem> Grid { get; set; }

        [Parameter]
        public RadzenDataGridGroupRow<TItem> Parent { get; set; }

        [Parameter]
        public Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder Builder { get; set; }

        public Group Group { get; set; }

        int GetLevel()
        {
            int i = 0;
            var p = Parent;
            while(p != null)
            {
                p = p.Parent;
                i++;
            }

            return i;
        }
}