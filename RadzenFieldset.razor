﻿@using Radzen.Blazor.Rendering
@inherits RadzenComponent

@if (Visible)
{
    <fieldset @ref="@Element" @attributes="Attributes" class="@GetCssClass()" style="@Style" id="@GetId()">
        @if(AllowCollapse || !string.IsNullOrEmpty(Text) || !string.IsNullOrEmpty(Icon) || HeaderTemplate != null)
        {
        <legend class="rz-fieldset-legend" style="white-space:nowrap">
            @if (AllowCollapse)
            {
                <a id="@(GetId() + "expc")" title="@TitleAttribute()" aria-label="@AriaLabelAttribute()" @onclick:preventDefault="true"
                   aria-controls="rz-fieldset-0-content" aria-expanded="@(collapsed ? "false" : "true")" @onclick=@Toggle
                       tabindex="0" @onkeypress="@(args => OnKeyPress(args, Toggle(new EventArgs())))" @onkeypress:preventDefault=preventKeyPress @onkeypress:stopPropagation>
                @if (collapsed)
                {
                    <span class="notranslate rz-fieldset-toggler rzi rzi-w rzi-plus"></span>
                }
                else
                {
                    <span class="notranslate rz-fieldset-toggler rzi rzi-w rzi-minus"></span>
                }

                @if (!string.IsNullOrEmpty(Icon))
                {
                    <i class="notranslate rzi" style="@(!string.IsNullOrEmpty(IconColor) ? $"color:{IconColor}" : null)">@Icon</i><span>@Text</span>
                }
                else
                {
                    <span class="rz-fieldset-legend-text">@Text</span>
                }
                @HeaderTemplate
                </a>
            } 
            else
            {
                @if (!string.IsNullOrEmpty(Icon))
                {
                    <i class="notranslate rzi" style="@(!string.IsNullOrEmpty(IconColor) ? $"color:{IconColor}" : null)">@Icon</i><span>@Text</span>
                }
                else
                {
                    <span class="rz-fieldset-legend-text">@Text</span>
                }
                @HeaderTemplate
            }
        </legend>
        }
        <Expander Expanded=@(!collapsed) CssClass="rz-fieldset-content-wrapper" id="rz-fieldset-0-content">
            <div class="rz-fieldset-content">
                @ChildContent
            </div>
        </Expander>
        @if (SummaryTemplate != null)
        {
        <Expander Expanded=@collapsed CssClass="rz-fieldset-content-wrapper">
            <div class="rz-fieldset-content rz-fieldset-content-summary">
                @SummaryTemplate
            </div>
        </Expander>
        }
    </fieldset>
}