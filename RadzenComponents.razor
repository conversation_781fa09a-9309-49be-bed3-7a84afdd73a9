﻿<RadzenDialog @attributes="Attributes" />
<RadzenNotification @attributes="Attributes" />
<RadzenContextMenu @attributes="Attributes" />
<RadzenTooltip @attributes="Attributes" />
<RadzenChartTooltip @attributes="Attributes" />

@code {
    /// <summary>
    /// Specifies additional custom attributes that will be rendered by the component.
    /// </summary>
    /// <value>The attributes.</value>
    [Parameter(CaptureUnmatchedValues = true)]
    public IReadOnlyDictionary<string, object> Attributes { get; set; }
}