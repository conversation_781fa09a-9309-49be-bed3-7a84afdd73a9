@typeparam TItem
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.JSInterop
@inject IJSRuntime JSRuntime
 <button title=@Column.GetFilterOperatorText(Column.GetFilterOperator()) class="@FilterIconStyle()" onclick="@($"Radzen.togglePopup(this.parentNode, '{filterId}')")">
     <i class="notranslate rzi">@Grid.FilterIcon</i>
    @if (Column.GetFilterOperator() == FilterOperator.DoesNotContain)
    {
        <s>@Column.GetFilterOperatorSymbol(Column.GetFilterOperator())</s>
    }
    else
    {
        @Column.GetFilterOperatorSymbol(Column.GetFilterOperator())
    }
</button>
<div id="@($"{filterId}")" class="rz-overlaypanel"
        style="display:none;" tabindex="0">
    <div class="rz-overlaypanel-content">
        <ul class="rz-listbox-list">
            @if (QueryableExtension.IsEnumerable(Column.FilterPropertyType))
            {
                @if (Column.GetFilterOperators().Contains(FilterOperator.Contains))
                {
            <li class="@(FilterOperatorStyle(Column, FilterOperator.Contains))" @onclick="@(args => ApplyFilter(FilterOperator.Contains))" style="display: block;">
                <span class="rz-filter-menu-symbol">@Column.GetFilterOperatorSymbol(FilterOperator.Contains)</span><span>@Grid.ContainsText</span>
            </li>
                }
                @if (Column.GetFilterOperators().Contains(FilterOperator.DoesNotContain))
                {
            <li class="@(FilterOperatorStyle(Column, FilterOperator.DoesNotContain))" @onclick="@(args => ApplyFilter(FilterOperator.DoesNotContain))" style="display: block;">
                <span class="rz-filter-menu-symbol"><s>@Column.GetFilterOperatorSymbol(FilterOperator.DoesNotContain)</s></span><span>@Grid.DoesNotContainText</span>
            </li>
                }
                @if (Column.GetFilterOperators().Contains(FilterOperator.In))
                {
            <li class="@(FilterOperatorStyle(Column, FilterOperator.In))" @onclick="@(args => ApplyFilter(FilterOperator.In))" style="display: block;">
                <span class="rz-filter-menu-symbol">@Column.GetFilterOperatorSymbol(FilterOperator.In)</span><span>@Grid.InText</span>
            </li>
                }
                @if (Column.GetFilterOperators().Contains(FilterOperator.NotIn))
                {
            <li class="@(FilterOperatorStyle(Column, FilterOperator.NotIn))" @onclick="@(args => ApplyFilter(FilterOperator.NotIn))" style="display: block;">
                <span class="rz-filter-menu-symbol"><s>@Column.GetFilterOperatorSymbol(FilterOperator.NotIn)</s></span><span>@Grid.NotInText</span>
            </li>
                }
                @if (Column.GetFilterOperators().Contains(FilterOperator.StartsWith) && Column.FilterPropertyType == typeof(string))
                {
            <li class="@(FilterOperatorStyle(Column, FilterOperator.StartsWith))" @onclick="@(args => ApplyFilter(FilterOperator.StartsWith))" style="display: block;">
                <span class="rz-filter-menu-symbol">@Column.GetFilterOperatorSymbol(FilterOperator.StartsWith)</span><span>@Grid.StartsWithText</span>
            </li>
                }
                @if (Column.GetFilterOperators().Contains(FilterOperator.EndsWith) && Column.FilterPropertyType == typeof(string))
                {
            <li class="@(FilterOperatorStyle(Column, FilterOperator.EndsWith))" @onclick="@(args => ApplyFilter(FilterOperator.EndsWith))" style="display: block;">
                <span class="rz-filter-menu-symbol">@Column.GetFilterOperatorSymbol(FilterOperator.EndsWith)</span><span>@Grid.EndsWithText</span>
            </li>
                }
                @if (Column.GetFilterOperators().Contains(FilterOperator.IsEmpty) && Column.FilterPropertyType == typeof(string))
                {
            <li class="@(FilterOperatorStyle(Column, FilterOperator.IsEmpty))" @onclick="@(args => ApplyFilter(FilterOperator.IsEmpty))" style="display: block;">
                <span class="rz-filter-menu-symbol">@Column.GetFilterOperatorSymbol(FilterOperator.IsEmpty)</span><span>@Grid.IsEmptyText</span>
            </li>
                }
                @if (Column.GetFilterOperators().Contains(FilterOperator.IsNotEmpty) && Column.FilterPropertyType == typeof(string))
                {
            <li class="@(FilterOperatorStyle(Column, FilterOperator.IsNotEmpty))" @onclick="@(args => ApplyFilter(FilterOperator.IsNotEmpty))" style="display: block;">
                <span class="rz-filter-menu-symbol">@Column.GetFilterOperatorSymbol(FilterOperator.IsNotEmpty)</span><span>@Grid.IsNotEmptyText</span>
            </li>
                }
            }
                @if (Column.GetFilterOperators().Contains(FilterOperator.Equals))
                {
            <li class="@(FilterOperatorStyle(Column, FilterOperator.Equals))" @onclick="@(args => ApplyFilter(FilterOperator.Equals))" style="display: block;">
                <span class="rz-filter-menu-symbol">@Column.GetFilterOperatorSymbol(FilterOperator.Equals)</span><span>@Grid.EqualsText</span>
            </li>
                }
                @if (Column.GetFilterOperators().Contains(FilterOperator.NotEquals))
                {
            <li class="@(FilterOperatorStyle(Column, FilterOperator.NotEquals))" @onclick="@(args => ApplyFilter(FilterOperator.NotEquals))" style="display: block;">
                <span class="rz-filter-menu-symbol">@Column.GetFilterOperatorSymbol(FilterOperator.NotEquals)</span><span>@Grid.NotEqualsText</span>
            </li>
                }
                @if (Column.GetFilterOperators().Contains(FilterOperator.LessThan))
                {
            <li class="@(FilterOperatorStyle(Column, FilterOperator.LessThan))" @onclick="@(args => ApplyFilter(FilterOperator.LessThan))" style="display: block;">
                <span class="rz-filter-menu-symbol">@Column.GetFilterOperatorSymbol(FilterOperator.LessThan)</span><span>@Grid.LessThanText</span>
            </li>
                }
                @if (Column.GetFilterOperators().Contains(FilterOperator.LessThanOrEquals))
                {
            <li class="@(FilterOperatorStyle(Column, FilterOperator.LessThanOrEquals))" @onclick="@(args => ApplyFilter(FilterOperator.LessThanOrEquals))" style="display: block;">
                <span class="rz-filter-menu-symbol">@Column.GetFilterOperatorSymbol(FilterOperator.LessThanOrEquals)</span><span>@Grid.LessThanOrEqualsText</span>
            </li>
                }
                @if (Column.GetFilterOperators().Contains(FilterOperator.GreaterThan))
                {
            <li class="@(FilterOperatorStyle(Column, FilterOperator.GreaterThan))" @onclick="@(args => ApplyFilter(FilterOperator.GreaterThan))" style="display: block;">
                <span class="rz-filter-menu-symbol">@Column.GetFilterOperatorSymbol(FilterOperator.GreaterThan)</span><span>@Grid.GreaterThanText</span>
            </li>
                }
                @if (Column.GetFilterOperators().Contains(FilterOperator.GreaterThanOrEquals))
                {
            <li class="@(FilterOperatorStyle(Column, FilterOperator.GreaterThanOrEquals))" @onclick="@(args => ApplyFilter(FilterOperator.GreaterThanOrEquals))" style="display: block;">
                <span class="rz-filter-menu-symbol">@Column.GetFilterOperatorSymbol(FilterOperator.GreaterThanOrEquals)</span><span>@Grid.GreaterThanOrEqualsText</span>
            </li>
                }
                @if (Column.GetFilterOperators().Contains(FilterOperator.IsNull))
                {
            <li class="@(FilterOperatorStyle(Column, FilterOperator.IsNull))" @onclick="@(args => ApplyFilter(FilterOperator.IsNull))" style="display: block;">
                <span class="rz-filter-menu-symbol">@Column.GetFilterOperatorSymbol(FilterOperator.IsNull)</span><span>@Grid.IsNullText</span>
            </li>
                }
                @if (Column.GetFilterOperators().Contains(FilterOperator.IsNotNull))
                {
            <li class="@(FilterOperatorStyle(Column, FilterOperator.IsNotNull))" @onclick="@(args => ApplyFilter(FilterOperator.IsNotNull))" style="display: block;">
                <span class="rz-filter-menu-symbol">@Column.GetFilterOperatorSymbol(FilterOperator.IsNotNull)</span><span>@Grid.IsNotNullText</span>
            </li>
                }
            <li class="rz-multiselect-item" @onclick="@(args => ClearFilter())" style="display:block;">
                <span class="rz-filter-menu-symbol">x</span><span>@Grid.ClearFilterText</span>
            </li>
        </ul>
    </div>
</div>
@code {
    [Parameter]
    public RadzenDataGridColumn<TItem> Column { get; set; }

    [Parameter]
    public RadzenDataGrid<TItem> Grid { get; set; }

    string filterId;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        filterId = $"{Grid.PopupID}{Column.GetFilterProperty()}fm";
    }

    protected string FilterOperatorStyle(RadzenDataGridColumn<TItem> column, FilterOperator value)
    {
        return column.GetFilterOperator() == value ?
            "rz-multiselect-item rz-state-highlight" :
            "rz-multiselect-item";
    }

    protected string FilterIconStyle()
    {
        var additionalStyle = Column.HasActiveFilter() ? "rz-grid-filter-active" : "";

        return $"rz-filter-button rz-button rz-button-md rz-button-icon-only rz-variant-flat rz-base rz-shade-default {additionalStyle}";
    }

    protected async Task ApplyFilter(FilterOperator value)
    {
        if (value == FilterOperator.IsNull || value == FilterOperator.IsNotNull 
            || value == FilterOperator.IsEmpty || value == FilterOperator.IsNotEmpty)
        {
            Column.SetFilterValue(value == FilterOperator.IsEmpty || value == FilterOperator.IsNotEmpty ? string.Empty : null);
            Column.SetFilterValue(null, false);
        }

        Column.SetFilterOperator(value);

        Grid.SaveSettings();

        await Grid.Filter.InvokeAsync(new DataGridColumnFilterEventArgs<TItem>()
        {
            Column = Column,
            FilterValue = Column.GetFilterValue(),
            SecondFilterValue = Column.GetSecondFilterValue(),
            FilterOperator = Column.GetFilterOperator(),
            SecondFilterOperator = Column.GetSecondFilterOperator(),
            LogicalFilterOperator = Column.GetLogicalFilterOperator()
        });

        await JSRuntime.InvokeVoidAsync("Radzen.closePopup", $"{filterId}");
        await Grid.ReloadInternal();
    }

    protected async Task ClearFilter()
    {
        Column.ClearFilters();

        Grid.SaveSettings();

        await JSRuntime.InvokeVoidAsync("Radzen.closePopup", $"{filterId}");

        await Grid.FilterCleared.InvokeAsync(new DataGridColumnFilterEventArgs<TItem>()
        {
            Column = Column,
            FilterValue = Column.GetFilterValue(),
            SecondFilterValue = Column.GetSecondFilterValue(),
            FilterOperator = Column.GetFilterOperator(),
            SecondFilterOperator = Column.GetSecondFilterOperator(),
            LogicalFilterOperator = Column.GetLogicalFilterOperator()
        });

        await Grid.ReloadInternal();
    }
}
