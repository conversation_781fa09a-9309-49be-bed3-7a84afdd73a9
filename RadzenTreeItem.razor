@using <PERSON><PERSON><PERSON>
@using Ra<PERSON>zen.Blazor.Rendering
@implements IDisposable
@{var itemArgs = Tree?.ItemAttributes(this); }
<li class="rz-treenode" @attributes="@(itemArgs.Item1.Attributes.Any() ? itemArgs.Item1.Attributes : Attributes)" 
    @oncontextmenu="OnContextMenu" @oncontextmenu:preventDefault="@Tree.ItemContextMenu.HasDelegate" @oncontextmenu:stopPropagation>
    <div class=@ContentClass @onclick="@Select">
        @if (ChildContent != null || HasChildren)
        {
            <span class=@IconClass @onclick="@Toggle" @onclick:stopPropagation></span>
        }
        @if(Tree != null && Tree.AllowCheckBoxes)
        {
            <RadzenCheckBox TabIndex=-1 Name="@($"{GetHashCode()}")" TValue="bool?" Disabled="@(!Checkable)" Value="@(itemArgs.Item1.CheckedSet() ? itemArgs.Item1.Checked : IsChecked())" Change="@CheckedChange" InputAttributes="@(new Dictionary<string,object>(){ { "aria-label", Tree.SelectItemAriaLabel }})" />
        }
        @if (Template != null)
        {
            <div class=@LabelClass @onkeydown:stopPropagation>@Template(this)</div>
        } else
        {
            <label for="@(GetHashCode())" class=@LabelClass>@Text</label>
        }
    </div>
    @if (ChildContent != null && expanded)
    {
    <CascadingValue Value=this>
    <Expander Expanded=@clientExpanded>
    <ul class="rz-treenode-children" @onkeydown:stopPropagation>
        @ChildContent
    </ul>
    </Expander>
    </CascadingValue>
    }
</li>
