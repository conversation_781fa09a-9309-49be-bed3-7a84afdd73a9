﻿@inherits RadzenComponentWithChildren
@typeparam TItem
@if(Visible)
{
<div @ref="@Element" style="@Style" @attributes="Attributes" class="@GetCssClass()" id="@GetId()" @ondrop="OnDrop" @ondragover="OnDragOver" @ondragleave="OnDragLeave">
    @ChildContent
    @foreach (var item in Items)
    {
        var result = ItemAttributes(item);

        <CascadingValue Value=@item>
            <CascadingValue Value=this>
                <RadzenDropZoneItem TItem="TItem" Visible="@result.Item1.Visible" Attributes="@result.Item2" />
            </CascadingValue>
        </CascadingValue>
    }
    @Footer
</div>
}
