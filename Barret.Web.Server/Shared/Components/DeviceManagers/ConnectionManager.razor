@using Barret.Core.Areas.Devices.Enums
@using Barret.Web.Server.Shared.Components.DeviceEditors
@using DevExpress.Blazor
@using Barret.Services.Core.Areas.Devices.Queries
@using Barret.Services.Core.Areas.Manufacturers
@using Barret.Services.Core.Areas.DeviceModels.Queries
@using Barret.Services.Core.Areas.Vehicles
@using Microsoft.Extensions.Logging
@using Barret.Shared.DTOs.Devices
@using Microsoft.AspNetCore.Components.Web
@using Barret.Web.Server.Services
@using Barret.Core.Areas.Vehicles.Models.Vessel
@using Barret.Shared.DTOs.Vehicles.Vessels
@using Barret.Shared.Factories
@using Barret.Web.Server.Extensions
@using Barret.Web.Server.Services.DTO
@using Barret.Web.Server.Features.Vehicles.Services

@inject IJSRuntime JSRuntime
@inject IDeviceQueryService DeviceQueryService
@inject IVehicleService VehicleService
@inject DeviceDtoService DeviceDtoService
@inject IBarretToastNotificationService ToastService
@inject ILogger<ConnectionManager> _logger
@inject IDialogService DialogService

<DxMessageBox @bind-Visible="deleteConnectionMessageVisible"
              Type="MessageBoxType.Confirmation"
              Title="Confirm Deletion"
              Text="@($"Are you sure you want to remove the connection to '{targetDeviceName}'?")"
              OkButtonText="Delete"
              CancelButtonText="Cancel"
              RenderStyle="MessageBoxRenderStyle.Danger"
              CloseOnEscape="true"
              Width="450px"
              Closed="@OnDeleteConnectionMessageBoxClosed">
</DxMessageBox>

<div class="connection-section">
    <div class="d-flex justify-content-between align-items-center mb-2">
        <h6 class="mb-0">Connections</h6>
        <button class="btn btn-sm btn-primary" @onclick="OpenAddConnectionDialog">
            <i class="bi bi-plus-lg"></i> Add
        </button>
    </div>

    @if (SourceDevice.Connections != null && SourceDevice.Connections.Any())
    {
        <div class="connection-container p-2">
            <DxGrid Data="@SourceDevice.Connections"
                    CssClass="compact-grid"
                    ShowFilterRow="true"
                    PageSize="5">
                <Columns>
                    <DxGridDataColumn Caption="Target Device" Width="30%">
                        <CellDisplayTemplate>
                            @{
                                var connection = (context.DataItem as DeviceConnectionDto);
                                var targetDevice = AllDevices?.FirstOrDefault(d => d.Id == connection?.InterfaceDeviceId);
                                <span>@(targetDevice?.Name ?? "Unknown Device")</span>
                            }
                        </CellDisplayTemplate>
                    </DxGridDataColumn>
                    <DxGridDataColumn FieldName="Type" Caption="Type" Width="25%" />
                    <DxGridDataColumn FieldName="Direction" Caption="Direction" Width="25%" />
                    <DxGridDataColumn Caption="Actions" Width="20%">
                        <CellDisplayTemplate>
                            @{
                                var connection = (context.DataItem as DeviceConnectionDto);
                                if (connection != null)
                                {
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-sm btn-outline-primary" @onclick="() => HandleEditConnection(connection)">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" @onclick="() => HandleDeleteConnection(connection)">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                }
                            }
                        </CellDisplayTemplate>
                    </DxGridDataColumn>
                </Columns>
            </DxGrid>
        </div>
    }
    else
    {
        <div class="alert alert-light py-2">No connections configured</div>
    }
</div>

<!-- Connection Editor Dialog -->
<DxPopup @bind-Visible="@isConnectionEditorVisible"
         HeaderText="@connectionEditorTitle"
         ShowFooter="true"
         Width="500px"
         CloseOnEscape="true"
         CloseOnOutsideClick="false">
    <HeaderTemplate>
        <div class="d-flex align-items-center">
            <i class="bi bi-link me-2"></i>
            <span>@connectionEditorTitle</span>
        </div>
    </HeaderTemplate>
    <Content>
        <div class="mb-3">
            <label class="form-label">Target Device</label>
            <DxComboBox Data="@availableTargetDevices"
                       @bind-Value="@selectedTargetDeviceId"
                       TextFieldName="Name"
                       ValueFieldName="Id"
                       CssClass="w-100"
                       NullText="Select a device..."
                       ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto">
            </DxComboBox>
        </div>
        <div class="mb-3">
            <label class="form-label">Connection Type</label>
            <DxComboBox Data="@ConnectionTypes"
                       @bind-Value="@selectedConnectionType"
                       CssClass="w-100"
                       NullText="Select a connection type...">
            </DxComboBox>
        </div>
        <div class="mb-3">
            <label class="form-label">Direction</label>
            <DxComboBox Data="@ConnectionDirections"
                       @bind-Value="@selectedConnectionDirection"
                       CssClass="w-100"
                       NullText="Select a direction...">
            </DxComboBox>
        </div>
    </Content>
    <FooterTemplate>
        <div class="d-flex justify-content-end gap-2">
            <RadzenButton Text="Cancel"
                         ButtonStyle="ButtonStyle.Secondary"
                         Click="@(() => isConnectionEditorVisible = false)"
                         class="px-4 py-2" />
            <RadzenButton Text="Save"
                         ButtonStyle="ButtonStyle.Primary"
                         Click="@SaveConnection"
                         Disabled="@(selectedTargetDeviceId == Guid.Empty || selectedTargetDeviceId == SourceDevice.Id)"
                         class="px-4 py-2" />
        </div>
    </FooterTemplate>
</DxPopup>

@code {
    [Parameter]
    public required DeviceDto SourceDevice { get; set; }

    [Parameter]
    public required List<DeviceDto> AllDevices { get; set; }

    [Parameter]
    public VesselDto? VesselDto { get; set; }

    [Parameter]
    public EventCallback<DeviceDto> OnDeviceChanged { get; set; }

    private bool isLoading = false;
    private string errorMessage = string.Empty;
    private bool _operationInProgress = false;
    private bool deleteConnectionMessageVisible = false;
    private Guid targetDeviceIdToDelete;
    private string targetDeviceName = "the selected device";

    // Connection editor properties
    private bool isConnectionEditorVisible = false;
    private string connectionEditorTitle = "Add Connection";
    private Guid selectedTargetDeviceId = Guid.Empty;
    private ConnectionType selectedConnectionType = ConnectionType.Standard;
    private ConnectionDirection selectedConnectionDirection = ConnectionDirection.Duplex;
    private bool isEditingExistingConnection = false;
    private List<DeviceDto> availableTargetDevices = new List<DeviceDto>();

    // Pre-populated lists for enums to avoid Razor component attribute issues
    private ConnectionType[] ConnectionTypes => Enum.GetValues<ConnectionType>();
    private ConnectionDirection[] ConnectionDirections => Enum.GetValues<ConnectionDirection>();

    protected override void OnParametersSet()
    {
        // Filter out the source device and use compatibility system
        if (AllDevices != null && SourceDevice != null)
        {
            // Get devices that can serve as interfaces for the source device
            availableTargetDevices = VehicleService
                .GetCompatibleInterfaceDevices(SourceDevice, AllDevices)
                .ToList();
        }
    }

    private void OpenAddConnectionDialog()
    {
        if (_operationInProgress)
        {
            return; // Prevent multiple operations
        }

        try
        {
            _operationInProgress = true;

            // Reset the form
            connectionEditorTitle = "Add Connection";
            selectedTargetDeviceId = Guid.Empty;
            selectedConnectionType = ConnectionType.Standard;
            selectedConnectionDirection = ConnectionDirection.Duplex;
            isEditingExistingConnection = false;

            // Show the dialog
            isConnectionEditorVisible = true;
        }
        finally
        {
            _operationInProgress = false;
            StateHasChanged();
        }
    }

    private void HandleEditConnection(DeviceConnectionDto connection)
    {
        if (_operationInProgress)
        {
            return; // Prevent multiple operations
        }

        try
        {
            _operationInProgress = true;

            // Set the form values
            connectionEditorTitle = "Edit Connection";
            selectedTargetDeviceId = connection.InterfaceDeviceId;
            selectedConnectionType = connection.Type;
            selectedConnectionDirection = connection.Direction;
            isEditingExistingConnection = true;

            // Show the dialog
            isConnectionEditorVisible = true;
        }
        finally
        {
            _operationInProgress = false;
            StateHasChanged();
        }
    }

    private void HandleDeleteConnection(DeviceConnectionDto connection)
    {
        if (_operationInProgress)
        {
            return; // Prevent multiple operations
        }

        // Get the target device name for the confirmation message
        var targetDevice = AllDevices?.FirstOrDefault(d => d.Id == connection.InterfaceDeviceId);
        targetDeviceName = targetDevice?.Name ?? "the selected device";

        // Store the target device ID to delete
        targetDeviceIdToDelete = connection.InterfaceDeviceId;

        // Show the confirmation dialog
        deleteConnectionMessageVisible = true;
    }

    private async void OnDeleteConnectionMessageBoxClosed(bool confirmed)
    {
        if (confirmed)
        {
            await ConfirmDeleteConnection();
        }

        // Reset the delete message box state
        deleteConnectionMessageVisible = false;
    }

    private async Task ConfirmDeleteConnection()
    {
        try
        {
            _operationInProgress = true;

            // Remove the connection using the DeviceDtoService
            var (removed, errorMessage) = DeviceDtoService.RemoveConnection(SourceDevice, targetDeviceIdToDelete);

            if (removed)
            {
                // Also remove the connection from the vessel's DeviceConnections list if available
                if (VesselDto != null)
                {
                    var vesselConnection = VesselDto.DeviceConnections.FirstOrDefault(c =>
                        c.ConnectedDeviceId == SourceDevice.Id &&
                        c.InterfaceDeviceId == targetDeviceIdToDelete);

                    if (vesselConnection != null)
                    {
                        VesselDto.DeviceConnections.Remove(vesselConnection);
                        _logger.LogInformation("Removed connection from vessel's DeviceConnections list");
                    }
                }

                // Notify the parent component that the device has changed
                await OnDeviceChanged.InvokeAsync(SourceDevice);

                ToastService.ShowToast(new ToastOptions
                {
                    Title = "Success",
                    Text = "Connection removed successfully",
                    IconCssClass = "bi bi-check-circle-fill",
                    CssClass = "toast-success",
                    ShowIcon = true
                });
            }
            else
            {
                ToastService.ShowToast(new ToastOptions
                {
                    Title = "Error",
                    Text = errorMessage ?? "Failed to remove connection",
                    IconCssClass = "bi bi-exclamation-circle-fill",
                    CssClass = "toast-error",
                    ShowIcon = true
                });
            }
        }
        catch (Exception ex)
        {
            ToastService.ShowToast(new ToastOptions
            {
                Title = "Error",
                Text = $"Error removing connection: {ex.Message}",
                IconCssClass = "bi bi-exclamation-circle-fill",
                CssClass = "toast-error",
                ShowIcon = true
            });
        }
        finally
        {
            _operationInProgress = false;
            StateHasChanged();
        }
    }

    private async Task SaveConnection()
    {
        try
        {
            _operationInProgress = true;

            if (isEditingExistingConnection)
            {
                // Update the existing connection
                bool updated = DeviceDtoService.UpdateConnection(
                    SourceDevice,
                    selectedTargetDeviceId,
                    selectedConnectionType,
                    selectedConnectionDirection);

                if (updated)
                {
                    // Notify the parent component that the device has changed
                    await OnDeviceChanged.InvokeAsync(SourceDevice);

                    ToastService.ShowToast(new ToastOptions
                    {
                        Title = "Success",
                        Text = "Connection updated successfully",
                        IconCssClass = "bi bi-check-circle-fill",
                        CssClass = "toast-success",
                        ShowIcon = true
                    });
                }
                else
                {
                    ToastService.ShowToast(new ToastOptions
                    {
                        Title = "Error",
                        Text = "Failed to update connection",
                        IconCssClass = "bi bi-exclamation-circle-fill",
                        CssClass = "toast-error",
                        ShowIcon = true
                    });
                }
            }
            else
            {
                // Validate the connection using the VehicleService
                var targetDevice = AllDevices.FirstOrDefault(d => d.Id == selectedTargetDeviceId);
                if (targetDevice == null)
                {
                    ToastService.ShowToast(new ToastOptions
                    {
                        Title = "Error",
                        Text = "Selected device not found",
                        IconCssClass = "bi bi-exclamation-circle-fill",
                        CssClass = "toast-error",
                        ShowIcon = true
                    });
                    return;
                }

                // Check compatibility
                bool isCompatible = VehicleService.CanDevicesConnect(SourceDevice, targetDevice);
                if (!isCompatible)
                {
                    ToastService.ShowToast(new ToastOptions
                    {
                        Title = "Error",
                        Text = "These devices are not compatible",
                        IconCssClass = "bi bi-exclamation-circle-fill",
                        CssClass = "toast-error",
                        ShowIcon = true
                    });
                    return;
                }

                // Add a new connection
                var (connection, errorMessage) = DeviceDtoService.AddConnection(
                    SourceDevice,
                    selectedTargetDeviceId,
                    selectedConnectionType,
                    selectedConnectionDirection);

                if (connection != null)
                {
                    // Notify the parent component that the device has changed
                    await OnDeviceChanged.InvokeAsync(SourceDevice);

                    ToastService.ShowToast(new ToastOptions
                    {
                        Title = "Success",
                        Text = "Connection added successfully",
                        IconCssClass = "bi bi-check-circle-fill",
                        CssClass = "toast-success",
                        ShowIcon = true
                    });
                }
                else
                {
                    ToastService.ShowToast(new ToastOptions
                    {
                        Title = "Error",
                        Text = errorMessage ?? "Failed to add connection",
                        IconCssClass = "bi bi-exclamation-circle-fill",
                        CssClass = "toast-error",
                        ShowIcon = true
                    });
                }
            }

            // Close the dialog
            isConnectionEditorVisible = false;
        }
        catch (Exception ex)
        {
            ToastService.ShowToast(new ToastOptions
            {
                Title = "Error",
                Text = $"Error saving connection: {ex.Message}",
                IconCssClass = "bi bi-exclamation-circle-fill",
                CssClass = "toast-error",
                ShowIcon = true
            });
        }
        finally
        {
            _operationInProgress = false;
            StateHasChanged();
        }
    }
}
