﻿@inherits RadzenComponentWithChildren

@if (Visible)
{
    <ul @ref="@Element" style="@Style" @attributes="Attributes" class="@GetCssClass()" id="@GetId()" 
        tabindex="0" @onkeydown="@OnKeyPress" @onkeydown:preventDefault=preventKeyPress @onkeydown:stopPropagation>
        <li class="rz-navigation-item">
            <div class="rz-navigation-item-wrapper" onclick="Radzen.toggleMenuItem(this)">
                <div class="rz-navigation-item-link">
                    <div class="item-text" @onkeydown:stopPropagation>
                        @if (Template != null)
                        {
                            @Template
                        }
                    </div>
                    @if (ShowIcon)
                    {
                        <i class=@ToggleClass>keyboard_arrow_down</i>
                    }
                </div>
            </div>
            <ul class="rz-navigation-menu" style="@contentStyle" @onkeydown:stopPropagation>
                <CascadingValue Value=this>
                    @ChildContent
                </CascadingValue>
            </ul>
        </li>
    </ul>
}
