@using Radzen.Blazor
@inherits RadzenComponent

@if (Visible)
{
<div @ref="@Element" for="@Component" style="@Style" @attributes="Attributes" class="@GetCssClass()" id="@GetId()" onfocusin="this.classList.add('rz-state-focused')" onfocusout="this.classList.remove('rz-state-focused')">
    <div class="rz-form-field-content">
        <CascadingValue Value=@context>
        @if (Start != null)
        {
        <div class="rz-form-field-start">
            @Start
        </div>
        }
        @ChildContent
        <label class="rz-form-field-label rz-text-truncate" for=@Component>  @if (TextTemplate is null) {@Text} else {@TextTemplate} </label>
        @if (End != null)
        {
        <div class="rz-form-field-end">
            @End
        </div>
        }
        </CascadingValue>
    </div>
    @if (Helper != null)
    {
    <div class="rz-form-field-helper">
        @Helper
    </div>
    }
</div>
}
