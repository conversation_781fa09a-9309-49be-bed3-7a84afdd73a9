﻿@inherits RadzenComponent

@if (Visible)
{
    <button @ref="@Element" style="@Style" disabled="@IsDisabled" tabindex="@(Disabled ? -1 : TabIndex)"
            type="@Enum.GetName(typeof(ButtonType), ButtonType).ToLower()"
            @attributes="Attributes" class="@GetCssClass()" id="@GetId()"
            @onclick="@OnClick">
        <span class="rz-button-box">
            @if (ChildContent != null)
            {
                @ChildContent
            }
            else
            {
                @if (IsBusy)
                {
                    <RadzenIcon Icon="refresh" Style="animation: rotation 700ms linear infinite" />
                    @if (!string.IsNullOrEmpty(BusyText))
                    {
                        <span class="rz-button-text">@BusyText</span>
                    }
                }
                else
                {
                    @if (!string.IsNullOrEmpty(@Icon))
                    {
                        <i class="notranslate rz-button-icon-left rzi" style="@(!string.IsNullOrEmpty(IconColor) ? $"color:{IconColor}" : null)">@Icon</i>
                    }
                    @if (!string.IsNullOrEmpty(Image))
                    {
                        <img class="notranslate rz-button-icon-left rzi" src="@Image" alt="@ImageAlternateText" />
                    }
                    @if (!string.IsNullOrEmpty(Text))
                    {
                        <span class="rz-button-text">@Text</span>
                    }
                }
            }
        </span>
    </button>
}
