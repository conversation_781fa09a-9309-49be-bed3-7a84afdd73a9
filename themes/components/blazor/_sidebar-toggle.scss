$sidebar-toggle-icon-width: 1.5rem !default;
$sidebar-toggle-icon-height: 1.5rem !default;
$sidebar-toggle-margin-inline-end: 1rem !default;
$sidebar-toggle-padding: 0.8125rem !default;
$sidebar-toggle-border: var(--rz-border-base-200) !default;
$sidebar-toggle-color: var(--rz-base-900) !default;
$sidebar-toggle-background-color: var(--rz-header-background-color) !default;
$sidebar-toggle-hover-color: var(--rz-secondary) !default;
$sidebar-toggle-hover-background-color: var(--rz-header-background-color) !default;
$sidebar-toggle-hover-border-radius: 0 !default;
$sidebar-toggle-focus-outline: var(--rz-outline-focus) !default;
$sidebar-toggle-focus-outline-offset: calc(-1 * var(--rz-outline-offset)) !default;

.rz-sidebar-toggle {
  appearance: none;
  cursor: pointer;
  border: none;
  padding: var(--rz-sidebar-toggle-padding);
  margin-inline-end: var(--rz-sidebar-toggle-margin-inline-end);
  border-inline-end: var(--rz-sidebar-toggle-border);
  background-color: var(--rz-sidebar-toggle-background-color);
  color: var(--rz-sidebar-toggle-color);

  .rz-justify-content-flex-end & {
    border-inline-start: var(--rz-sidebar-toggle-border);
    border-inline-end: none;
  }

  &:focus {
    outline: none;
  }

  &:focus-visible {
    outline: var(--rz-sidebar-toggle-focus-outline);
    outline-offset: var(--rz-sidebar-toggle-focus-outline-offset);
  }

  &:focus-visible,
  &:hover {
    color: var(--rz-sidebar-toggle-hover-color);
    background: var(--rz-sidebar-toggle-hover-background-color);
    border-radius: var(--rz-sidebar-toggle-hover-border-radius);
  }

  .rzi {
    width: var(--rz-sidebar-toggle-icon-width);
    height: var(--rz-sidebar-toggle-icon-height);
    font-size: var(--rz-sidebar-toggle-icon-width);
  }
}
