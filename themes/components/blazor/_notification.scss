$notification-padding: 1rem 1.25rem !default;
$notification-gap: 0.625rem !default;
$notification-icon-margin: 0 !default;
$notification-container-background-color: transparent !default;
$notification-shadow: none !default;
$notification-border-radius: var(--rz-border-radius) !default;
$notification-success-color: var(--rz-on-success) !default;
$notification-success-background-color: var(--rz-success) !default;
$notification-success-icon-color: $notification-success-color !default;
$notification-warning-color: var(--rz-on-warning) !default;
$notification-warning-background-color: var(--rz-warning) !default;
$notification-warning-icon-color: $notification-warning-color !default;
$notification-error-color: var(--rz-on-danger) !default;
$notification-error-background-color: var(--rz-danger) !default;
$notification-error-icon-color: $notification-error-color !default;
$notification-info-color: var(--rz-on-info) !default;
$notification-info-background-color: var(--rz-info) !default;
$notification-info-icon-color: $notification-info-color !default;

.rz-notification {
  box-sizing: border-box;
  position: fixed;
  z-index: 1002;
  inset-block-start: 100px;
  inset-inline-end: 1rem;
}

.rz-notification-item-wrapper {
  position: relative;
  width: 250px;
  min-width: 20rem;
  background-color: var(--rz-notification-container-background-color);
  margin: 1rem 0;
  
  & > .rz-progressbar {
    position: absolute;
    inset-block-end: var(--rz-notification-border-radius);
    inset-inline: var(--rz-notification-border-radius); 
    --rz-progressbar-height: 0.25rem;
    --rz-progressbar-border-radius: var(--rz-notification-border-radius); 
  }
  
  &:has(>.rz-progressbar) {
    .rz-notification-content {
      margin-block-end: var(--rz-notification-border-radius);
    }
  }
  
}

.rz-notification-item {
  display: flex;
  align-items: flex-start;
  gap: var(--rz-notification-gap);
  border-radius: var(--rz-notification-border-radius);
  box-shadow: var(--rz-notification-shadow);
  padding: var(--rz-notification-padding);

  .rz-notification-success & {
    color: var(--rz-notification-success-color);
    background-color: var(--rz-notification-success-background-color);
  }

  .rz-notification-warn & {
    color: var(--rz-notification-warning-color);
    background-color: var(--rz-notification-warning-background-color);
  }

  .rz-notification-error & {
    color: var(--rz-notification-error-color);
    background-color: var(--rz-notification-error-background-color);
  }

  .rz-notification-info & {
    color: var(--rz-notification-info-color);
    background-color: var(--rz-notification-info-background-color);
  }
}

.rz-notification-message-wrapper {
  flex: auto;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--rz-notification-gap);
}

.rz-notification-message {
  flex: auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.rz-notification-title {
  font-weight: bold;
}

.rz-notification-icon {
  margin: var(--rz-notification-icon-margin);

  &.rzi-check {
    color: var(--rz-notification-success-icon-color);

    &:before {
      content: "check";
    }
  }

  &.rzi-exclamation-triangle {
    color: var(--rz-notification-warning-icon-color);

    &:before {
      content: "warning";
    }
  }

  &.rzi-info-circle {
    color: var(--rz-notification-info-icon-color);

    &:before {
      content: "info";
    }
  }

  &.rzi-times {
    color: var(--rz-notification-error-icon-color);

    &:before {
      content: "error";
    }
  }
}

.rz-notification-close {
  cursor: pointer;
  opacity: 0.75;

  &:before {
    content: "close";
  }
}
