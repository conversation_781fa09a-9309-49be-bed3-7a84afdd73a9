$grid-data-border-shadow: none !default;
$grid-cell-border: var(--rz-border-normal) !default;
$grid-right-cell-border: $grid-cell-border !default;
$grid-bottom-cell-border: $grid-cell-border !default;
$grid-cell-padding: 0.5rem !default;
$grid-cell-color: var(--rz-text-color) !default;
$grid-cell-font-size: var(--rz-body-font-size) !default;
$grid-cell-line-height: var(--rz-body-line-height) !default;
$grid-cell-focus-background-color: var(--rz-secondary-lighter) !default;
$grid-cell-focus-color: var(--rz-on-secondary-lighter) !default;
$grid-cell-focus-outline: var(--rz-outline-focus) !default;
$grid-cell-focus-outline-offset: calc(-1 * var(--rz-outline-width)) !default;
$grid-hover-background-color: var(--rz-secondary-lighter) !default;
$grid-hover-color: var(--rz-on-secondary-lighter) !default;
$grid-focus-outline: var(--rz-outline-focus) !default;
$grid-focus-outline-offset: var(--rz-outline-offset) !default;
$grid-selected-background-color: var(--rz-secondary-dark) !default;
$grid-selected-color: var(--rz-on-secondary-dark) !default;
$grid-toolbar-background-color: var(--rz-base-background-color) !default;
$grid-header-cell-border: $grid-cell-border !default;
$grid-header-cell-border-bottom: none !default;
$grid-header-background-color: var(--rz-base-200) !default;
$grid-header-font-size: 0.75rem !default;
$grid-header-line-height: 1rem !default;
$grid-header-font-weight: 600 !default;
$grid-header-text-transform: uppercase !default;
$grid-header-color: var(--rz-text-color) !default;
$grid-header-cell-padding: 0.625rem 0 !default;
$grid-header-title-padding-inline: 0.5rem 0 !default;
$grid-header-sorted-background-color: var(--rz-base-300) !default;
$grid-header-padding: 1rem !default;
$grid-foot-cell-color: $grid-cell-color !default;
$grid-foot-background-color: $grid-header-background-color !default;
$grid-filter-background-color: var(--rz-base-100) !default;
$grid-filter-padding: 0.5rem !default;
$grid-filter-margin: 0 !default;
$grid-filter-border: $grid-cell-border !default;
$grid-filter-font-size: var(--rz-body-font-size) !default;
$grid-filter-icon-width: var(--rz-icon-size) !default;
$grid-filter-icon-height: $grid-filter-icon-width !default;
$grid-filter-icon-margin-inline: 0 0.25rem !default;
$grid-filter-icon-font-size: var(--rz-icon-size) !default;
$grid-filter-color: var(--rz-text-disabled-color) !default;
$grid-filter-focus-color: var(--rz-secondary) !default;
$grid-filter-gap: 1rem !default;
$grid-filter-buttons-padding: 0 1rem 1rem !default;
$grid-filter-buttons-border: none !default;
$grid-filter-buttons-background-color: var(--rz-base-100) !default;
$grid-filter-button-padding-inline: 1rem !default;
$grid-clear-filter-button-background-color: var(--rz-base) !default;
$grid-clear-filter-button-color: var(--rz-on-base) !default;
$grid-clear-filter-button-shadow: none !default;
$grid-apply-filter-button-background-color: var(--rz-primary) !default;
$grid-apply-filter-button-color: var(--rz-on-primary) !default;
$grid-apply-filter-button-shadow: none !default;
$grid-header-filter-icon-margin-inline: auto 0.5rem !default;
$grid-header-filter-icon-hover-color: var(--rz-text-color) !default;
$grid-header-filter-icon-active-color: var(--rz-primary) !default;
$grid-header-filter-icon-font-size: 1rem !default;
$grid-simple-filter-icon-active-color: var(--rz-on-secondary-lighter) !default;
$grid-simple-filter-icon-active-background-color: var(--rz-secondary-lighter) !default;
$grid-border: var(--rz-border-normal) !default;
$grid-border-radius: 0 !default;
$grid-sort-icon-width: 1rem !default;
$grid-sort-icon-height: $grid-sort-icon-width !default;
$grid-sort-icon-color: var(--rz-text-disabled-color) !default;
$grid-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.01) !default;
$grid-background-color: var(--rz-base-background-color) !default;
$grid-stripe-background-color: var(--rz-base-100) !default;
$grid-stripe-odd-background-color: $grid-background-color !default;
$grid-column-resizer-width: 0.5rem !default;
$grid-column-resizer-helper-width: 0.25rem !default;
$grid-column-resizer-helper-background-color: var(--rz-secondary) !default;
$grid-column-icon-width: 2rem !default;
$grid-column-icon-padding: 0 !default;
$grid-detail-template-border: none !default;
$grid-detail-template-border-radius: 0 !default;
$grid-detail-template-padding: 0 !default;
$grid-detail-template-background-color: var(--rz-base-background-color) !default;
$grid-loading-indicator-color: currentColor !default;
$grid-loading-indicator-background-color: rgba(var(--rz-base-600), 0.5) !default;
$grid-frozen-cell-border: $grid-cell-border !default;
$grid-frozen-cell-background-color: $grid-header-background-color !default;
$grid-state-transition: var(--rz-transition-all) !default;
$grid-group-header-padding: 0.5rem !default;
$grid-group-header-gap: 0.5rem !default;
$grid-group-header-item-color: var(--rz-text-color) !default;
$grid-group-header-item-background-color: var(--rz-base-background-color) !default;
$grid-group-header-item-padding-block: 0.5rem !default;
$grid-group-header-item-padding-inline: 0.5rem !default;
$grid-group-header-item-border: $grid-cell-border !default;
$grid-group-header-item-border-radius: var(--rz-border-radius) !default;
$grid-group-header-item-title-margin-inline: 0 0.5rem !default;
$column-drag-handle-color: $grid-header-color !default;
$column-drag-handle-hover-color: $column-drag-handle-color !default;
$column-drag-handle-margin-inline: 0 auto !default;
$column-draggable-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.1) !default;

.rz-datatable {
  position: relative;
  box-shadow: var(--rz-grid-shadow);
  border: var(--rz-grid-cell-border);
  border-radius: var(--rz-grid-border-radius);
  background-color: var(--rz-grid-background-color);
  overflow: hidden;

  &:focus {
    outline: var(--rz-outline-normal);
  }
  &:focus-visible {
    outline: var(--rz-grid-focus-outline);
    outline-offset: var(--rz-grid-focus-outline-offset);
  }

  .rz-col-icon {
    text-align: center;
    vertical-align: middle;
    width: var(--rz-grid-column-icon-width);
    padding: var(--rz-grid-column-icon-padding);
  }

  .rzi-chevron-circle-right {
    @extend %rzi;

    vertical-align: top;
    &:before {
      content: 'arrow_right';
    }

    /* Right-to-left arrow icons */
    *[dir="rtl"] & {
      transform: rotate(180deg);
    }
  }

  .rzi-chevron-circle-down {
    @extend %rzi;
    vertical-align: top;
    &:before {
      content: 'arrow_drop_down';
    }
  }

  &.rz-has-template {
    > .rz-datatable-scrollable-wrapper {
      > .rz-datatable-scrollable-view {
        > .rz-datatable-scrollable-body {
          > .rz-datatable-scrollable-table-wrapper {
            > table {
              > .rz-datatable-scrollable-colgroup col {
                &:first-child {
                  width: var(--rz-grid-column-icon-width);
                }
              }
            }
          }
        }
      }
    }
  }
}

.rz-unselectable-text {
  user-select: none;
}

.rz-datatable-tablewrapper,
.rz-datatable-scrollable-header-box,
.rz-datatable-scrollable-table-wrapper,
.rz-datatable-scrollable-footer-box {
  > table {
    table-layout: fixed;
    border-collapse: collapse;
    width: 100%;
  }
}

.rz-resizable-column {
  position: relative;
}

.rz-datatable-reorder-indicator-up {
  position: absolute;
  &:before {
    content: 'arrow_drop_down';
  }
}

.rz-datatable-reorder-indicator-down {
  position: absolute;

  &:before {
    content: 'arrow_drop_up';
  }
}

.rz-column-resizer {
  position: absolute;
  inset-block-start: 0;
  inset-inline-end: 0;
  height: 100%;
  cursor: col-resize;
  width: var(--rz-grid-column-resizer-width);

  &:after {
    content: "";
    position: absolute;
    inset-block-start: 0;
    inset-inline-end: 0;
    height: 100%;
    width: var(--rz-grid-column-resizer-helper-width);
    background-color: transparent;
  }

  &:hover,
  &:active {
    &:after {
      background-color: var(--rz-grid-column-resizer-helper-background-color);
    }
  }
}

.rz-rowgroup-header .fa,
.rz-row-toggler {
  color: var(--rz-grid-cell-color);
}

.rz-datatable-scrollable-footer {
  background-color: var(--rz-grid-header-background-color);
  border-top: var(--rz-grid-cell-border);
}

.rz-grid-table thead {

  th {
    background-color: var(--rz-grid-header-background-color);
    padding: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: inherit;
    border-bottom: var(--rz-grid-header-cell-border-bottom);

    &:not(:last-child) {
      border-inline-end: var(--rz-grid-header-cell-border);
    }

    > div:not(.rz-cell-filter) {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      width: 100%;
      outline: none;
      padding: var(--rz-grid-header-cell-padding);
    }

    &.rz-state-focused {
      outline: var(--rz-grid-cell-focus-outline);
      outline-offset: var(--rz-grid-cell-focus-outline-offset);
    }

    .rz-column-title {
      display: inline-flex;
      flex: auto;
      align-items: center;
      gap: 0.25rem;
      width: 100%;
      font-size: var(--rz-grid-header-font-size);
      line-height: var(--rz-grid-header-line-height);
      text-transform: var(--rz-grid-header-text-transform);
      color: var(--rz-grid-header-color);
      padding-inline: var(--rz-grid-header-title-padding-inline);
      font-weight: var(--rz-grid-header-font-weight);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      &:has(.rz-column-title-content > .rz-chkbox) {
        overflow: visible;
      }
    }

    .rz-column-title-content {
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      &:has(.rz-chkbox) {
        overflow: visible;
      }
    }

    &.rz-text-align-center {
      .rz-column-title {
        justify-content: center;
        padding-inline-start: 0;
      }
      &.rz-sortable-column {
        .rz-column-title {
          padding-inline-start: var(--rz-grid-sort-icon-width);
        }
      }
    }

    &.rz-text-align-right {
      .rz-column-title {
        justify-content: right;
      }
      &.rz-sortable-column {
        .rz-column-title {
          padding-inline-start: var(--rz-grid-sort-icon-width);
        }
      }
    }

    .rz-column-drag {
      + .rz-column-title {
        padding-inline-start: 0;
      }
    }
  }
}

.rz-datatable-tfoot, .rz-grid-table tfoot {
  td {
    background-color: var(--rz-grid-foot-background-color);
    font-size: var(--rz-grid-cell-font-size);
    line-height: var(--rz-grid-cell-line-height);
    color: var(--rz-grid-foot-cell-color);
    padding: var(--rz-grid-cell-padding);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &:not(:last-child) {
      border-inline-end: var(--rz-grid-right-cell-border);
    }

    &.rz-composite-cell {
      border-inline-end: var(--rz-grid-right-cell-border);
    }
  }
}

.rz-datatable-scrollable-header {
  background-color: var(--rz-grid-header-background-color);
  border-bottom: var(--rz-grid-bottom-cell-border);
}

.rz-datatable-scrollable-body {
  overflow: auto;
  border-top: none;
  flex: auto;
  border-bottom-right-radius: var(--rz-border-radius);
  border-bottom-left-radius: var(--rz-border-radius);
}

.rz-has-pager {
  .rz-datatable-scrollable-body {
    border-radius: 0;
  }
}
.rz-sortable-column {
  cursor: pointer;

  &:focus {
    outline: none;
  }

  &.rz-state-active {
    background-color: var(--rz-grid-header-sorted-background-color);
  }

  > div:hover {
    .rzi-sort:not(.rzi-sort-asc):not(.rzi-sort-desc) {
      color: var(--rz-grid-sort-icon-color);
    }
  }

  .rzi-grid-sort {
    @extend %rzi;
    width: var(--rz-grid-sort-icon-width);
    height: var(--rz-grid-sort-icon-height);
    font-size: var(--rz-grid-sort-icon-height);
    text-align: left;
  }

  .rzi-sort {
    color: transparent;

    &:before {
      content: 'sort';
    }
  }

  .rzi-sort-asc {
    color: var(--rz-grid-header-color);

    &:before {
      content: 'arrow_drop_up';
    }
  }

  .rzi-sort-desc {
    color: var(--rz-grid-header-color);

    &:before {
      content: 'arrow_drop_down';
    }
  }
}

.rz-grid-table-striped {
  tbody {
    > tr:not(.rz-expanded-row-content) {
      &:nth-child(even) {
        > td {
            background-color: var(--rz-grid-stripe-background-color);
        }
      }

      &:nth-child(odd) {
        > td {
          background-color: var(--rz-grid-stripe-odd-background-color);
        }
      }
    }
  }
}

.rz-grid-table-composite {
  & .rz-datatable-thead,
  &.rz-grid-table thead {
    th {
      border-bottom: var(--rz-grid-header-cell-border);

      &.rz-composite-cell {
        border-inline-end: var(--rz-grid-header-cell-border);
      }
    }
  }
}

.rz-datatable-data,
.rz-grid-table {
  td {
    padding: var(--rz-grid-cell-padding);
    border-bottom: var(--rz-grid-bottom-cell-border);

    &:not(:last-child) {
      border-inline-end: var(--rz-grid-right-cell-border);
    }

    &.rz-composite-cell {
      border-inline-end: var(--rz-grid-right-cell-border);
    }

    .rz-cell-data {
      color: var(--rz-grid-cell-color);
      font-size: var(--rz-grid-cell-font-size);
      line-height: var(--rz-grid-cell-line-height);
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &.rz-text-wrap {
        text-overflow: unset;
        overflow: visible;
      }

      &.rz-text-nowrap {
        text-overflow: unset;
        overflow: visible;
      }

      &:has(.rz-chkbox),
      &:has(.rz-button) {
        overflow: visible;
      }
    }

    .rz-cell-toggle {
      display: flex;
      align-items: center;
      gap: 0.25rem;
    }

    .rz-column-footer {
      color: var(--rz-grid-foot-cell-color);
    }
  }

  tr {
    td {
      &:first-child {
        border-inline-start: none;
      }
      &:last-child:not(.rz-composite-cell) {
        border-inline-end: none;
      }
    }

    &:first-child {
      > td {
        border-top: none;
      }
    }

    &:last-child {
      > td {
        border-bottom: none;
      }
    }
  }
}

// GridLines
.rz-grid-table {
  // GridLines.Both
  &.rz-grid-gridlines-both {
    > thead > tr > th,
    > thead > tr > th.rz-composite-cell,
    > tbody > tr > td,
    > tfoot > tr > td {
      border-bottom: var(--rz-grid-cell-border);

      &:not(:last-child) {
        border-inline-end: var(--rz-grid-cell-border);
      }
    }
  }

  // GridLines.None
  &.rz-grid-gridlines-none {
    > thead > tr > th,
    > thead > tr > th.rz-composite-cell,
    > tbody > tr > td,
    > tfoot > tr > td {
      border-bottom: none;
      border-inline-end: none;
    }
  }

  // GridLines.Horizontal
  &.rz-grid-gridlines-horizontal {
    > thead > tr > th,
    > thead > tr > th.rz-composite-cell,
    > tbody > tr > td,
    > tfoot > tr > td {
      border-bottom: var(--rz-grid-cell-border);
      border-inline-end: none;
    }
  }

  // GridLines.Vertical
  &.rz-grid-gridlines-vertical {
    > thead > tr > th,
    > thead > tr > th.rz-composite-cell,
    > tbody > tr > td,
    > tfoot > tr > td {
      border-bottom: none;

      &:not(:last-child) {
        border-inline-end: var(--rz-grid-cell-border);
      }
    }
  }
}

.rz-datatable-reflow {
  tbody {
    td {
      > .rz-column-title {
        display: none;
      }
    }
  }
}

.rz-datatable-scrollable {
  display: flex;
  flex-direction: column;

  &.rz-has-height {
    // IE 11 can't size without that
    > .rz-datatable-scrollable-wrapper {
      height: 0;

      > .rz-datatable-scrollable-view {
        height: 0;
      }
    }
  }
}

.rz-datatable-scrollable-wrapper {
  display: flex;
  flex-direction: column;
  flex: auto;
}

.rz-datatable-scrollable-view {
  display: flex;
  flex: auto;
  flex-direction: column;
  overflow: hidden;
}

.rz-datatable-header {
  background-color: var(--rz-grid-toolbar-background-color);
  padding: var(--rz-grid-header-padding);
  border-bottom: var(--rz-grid-bottom-cell-border);

  .rzi-plus {
    @extend %rzi;

    font-size: var(--rz-grid-cell-font-size);
    &:before {
      content: 'add';
    }
  }
}

.rz-cell-filter {
  padding: var(--rz-grid-filter-padding);
  margin: var(--rz-grid-filter-margin);
  border-top: var(--rz-grid-filter-border);
  font-size: var(--rz-grid-filter-font-size);
  font-weight: normal;

  .rz-cell-filter-label {
    display: flex;
    flex: auto;
    align-items: center;

    > .rzi {
      width: var(--rz-grid-filter-icon-width);
      height: var(--rz-grid-filter-icon-height);
      font-size: var(--rz-grid-filter-icon-font-size);
      margin-inline: var(--rz-grid-filter-icon-margin-inline);
      color: var(--rz-grid-filter-color);

      &.rz-cell-filter-clear {
        margin-inline-start: auto;
      }
    }

    .rz-current-filter {
      margin-inline-start: 0.5rem;
    }
  }
}

.rz-selectable {
  tbody {
    tr.rz-data-row {
      td,
      .rz-cell-data {
        transition: background-color var(--rz-transition), color var(--rz-transition);

        &.rz-frozen-cell {
          &:before,
          &:after {
            content: "";
            position: absolute;
            inset: 0;
            transition: background-color var(--rz-transition), color var(--rz-transition);
          }
        }

        &.rz-frozen-cell-left,
        &.rz-frozen-cell-left-inner {
          &:before {
            z-index: -1;
          }

          &:after {
            z-index: -2;
            background-color: var(--rz-grid-frozen-cell-background-color);
          }
        }

        &.rz-frozen-cell-right,
        &.rz-frozen-cell-right-inner {
          &:before {
            z-index: -2;
            background-color: var(--rz-grid-frozen-cell-background-color);
          }

          &:after {
            z-index: -1;
          }
        }
      }

      &.rz-state-highlight {
        > td {
          background-color: var(--rz-grid-selected-background-color);

          &.rz-frozen-cell-left,
          &.rz-frozen-cell-left-inner {
            &:before {
              background-color: var(--rz-grid-selected-background-color);
            }
          }

          &.rz-frozen-cell-right,
          &.rz-frozen-cell-right-inner {
            &:after {
              background-color: var(--rz-grid-selected-background-color);
            }
          }
        }

        .rz-cell-data {
          color: var(--rz-grid-selected-color);
        }

        > .rzi {
          color: var(--rz-grid-selected-color);
        }
      }

      &.rz-state-focused {
        > td {
          background-color: var(--rz-grid-cell-focus-background-color);
          color: var(--rz-grid-cell-focus-color);

          &.rz-frozen-cell-left,
          &.rz-frozen-cell-left-inner {
            &:before {
              background-color: var(--rz-grid-cell-focus-background-color);
              color: var(--rz-grid-cell-focus-color);
            }
          }

          &.rz-frozen-cell-right,
          &.rz-frozen-cell-right-inner {
            &:after {
              background-color: var(--rz-grid-cell-focus-background-color);
              color: var(--rz-grid-cell-focus-color);
            }
          }
        }

        > th.rz-state-focused {
          color: var(--rz-grid-cell-focus-color) !important;
        }

        .rz-cell-data {
          color: var(--rz-grid-cell-focus-color);
        }

        > .rzi {
          color: var(--rz-grid-cell-focus-color);
        }
      }

      &.rz-state-disabled {
        opacity: 0.5;
        pointer-events: none;
      }

      &:hover {
        &:not(.rz-state-highlight) {
          > td {
            background-color: var(--rz-grid-hover-background-color);

            &.rz-frozen-cell-left,
            &.rz-frozen-cell-left-inner {
              &:before {
                background-color: var(--rz-grid-hover-background-color);
              }
            }

            &.rz-frozen-cell-right,
            &.rz-frozen-cell-right-inner {
              &:after {
                background-color: var(--rz-grid-hover-background-color);
              }
            }
          }

          .rz-cell-data {
            color: var(--rz-grid-hover-color);
          }
        }
      }
    }
  }
}

.rz-cell-filter-content {
  display: flex;
  flex: auto;
  align-items: center;
  color: var(--rz-grid-filter-color);
  min-height: 1.375rem;
  container-type: inline-size;
  container-name: rz-cell-filter-content;
}

@container rz-cell-filter-content (max-width: 200px) {
  .rz-cell-filter-content {
    .rz-filter-button {
      .rzi {
        display: none;
      }
    }

    .rz-filter-button {
      position: absolute;
      z-index: 1;
      min-height: 0;

      @if $material == true or $fluent == true {
        inset-inline-start: calc(var(--rz-border-width) + 1px);
        height: calc(var(--rz-input-height) - var(--rz-border-width) * 2 - 2px);
        max-height: calc(var(--rz-input-height) - var(--rz-border-width) * 2 - 2px);
        line-height: calc(map-get($button-sizes, md, line-height) - var(--rz-border-width) * 2 - 2px);
        border-radius: calc(var(--rz-input-border-radius) - var(--rz-border-width) - 1px);
      }

      @else {
        inset-inline-start: calc(var(--rz-border-width));
        height: calc(var(--rz-input-height) - var(--rz-border-width) * 2);
        max-height: calc(var(--rz-input-height) - var(--rz-border-width) * 2);
        line-height: calc(map-get($button-sizes, md, line-height) - var(--rz-border-width) * 2);
        border-start-start-radius: calc(var(--rz-input-border-radius) - var(--rz-border-width));
        border-start-end-radius: 0;
        border-end-start-radius: calc(var(--rz-input-border-radius) - var(--rz-border-width));
        border-end-end-radius: 0;  
      }
    }

    &:has(.rz-filter-button) {
      .rz-cell-filter-label {
        position: relative;
        min-width: calc(2 * map-get($button-sizes, md, min-width));
      }
      
      .rz-textbox,
      .rz-inputtext {
        text-indent: map-get($button-sizes, md, min-width);
      }
    }
  }
}

.rz-expanded-row {
  > td {
    border-bottom: none;

    .rz-cell-data,
    .rz-row-toggler {
      color: var(--rz-grid-hover-color);
    }

    background-color: var(--rz-grid-hover-background-color);
  }
}

.rz-expanded-row-template {
  background-color: var(--rz-grid-detail-template-background-color);
  padding: var(--rz-grid-detail-template-padding);
  border: var(--rz-grid-detail-template-border);
  border-radius: var(--rz-grid-detail-template-border-radius);
}

.rz-expanded-row-content {
  > td {
    padding-top: 0;
    background-color: var(--rz-grid-detail-template-background-color);
  }
}

.rz-rowgroup-header {
  a {
    &:hover {
      text-decoration: none;
    }
  }
  td {
    border-top: var(--rz-grid-border);
    border-bottom: var(--rz-grid-border);
  }
}

.rz-datatable-loading {
  position: absolute;
  inset: 0;
  background-color: var(--rz-grid-loading-indicator-background-color);
  z-index: 2;
}

.rz-datatable-loading-content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: var(--rz-grid-loading-indicator-color);
  z-index: 2;

  .rzi-circle-o-notch {
    @extend %rzi;

    animation: rotation .5s linear infinite;
    font-size: 2rem;

    &:before {
      content: 'refresh';
    }
  }
}

@keyframes rotation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .rz-datatable-reflow {
    .rz-data-grid-data,
    .rz-datatable-tablewrapper,
    .rz-datatable-scrollable-header-box,
    .rz-datatable-scrollable-table-wrapper {
      > table {
        table-layout: auto;
        display: block;

        > tbody {
          display: block;
        }
      }
    }

    thead {
      th {
        display: none;
      }
    }

    .rz-data-row {
      display: block;
      > td {
        display: block;
        width: 100% !important;
        text-align: left !important;
        border: none;

        .rz-column-title {
          display: block;
        }
      }
    }
  }
}

.rz-grid-filter {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;

  .rz-dropdown,
  .rz-numeric,
  .rz-textbox,
  .rz-grid-filter-label {
    display: block;
    width: 100%;
  }

  .rz-grid-filter-label {
    font-weight: 600;
  }
}

.rz-grid-date-filter {
  width: 576px;
}

.rz-date-filter {
  display: flex;
  align-items: stretch;
  background-color: var(--rz-grid-filter-background-color);
  gap: var(--rz-grid-filter-gap);
  padding: var(--rz-grid-filter-gap);

  .rz-listbox {
    min-width: 11em;

    .rz-listbox-item {
      font-size: 0.875rem;
    }
  }

  .rz-datepicker-inline {
    border-radius: var(--rz-border-radius);

    table {
      width: 100%;
    }
  }
}

.rz-grid-filter-buttons {
  display: flex;
  justify-content: space-between;
  padding: var(--rz-grid-filter-buttons-padding);
  background-color: var(--rz-grid-filter-buttons-background-color);
  border-top: var(--rz-grid-filter-buttons-border);

  .rz-button.rz-shade-default{
    &.rz-clear-filter {
      background-color: var(--rz-grid-clear-filter-button-background-color);
      color: var(--rz-grid-clear-filter-button-color);
      box-shadow: var(--rz-grid-clear-filter-button-shadow);
      padding-inline: var(--rz-grid-filter-button-padding-inline);
    }
  
    &.rz-apply-filter {
      background-color: var(--rz-grid-apply-filter-button-background-color);
      color: var(--rz-grid-apply-filter-button-color);
      box-shadow: var(--rz-grid-apply-filter-button-shadow);
      padding-inline: var(--rz-grid-filter-button-padding-inline);
    }
  }
}

.rz-grid-filter-icon {
  justify-self: flex-end;
  color: var(--rz-grid-filter-color);
  margin-inline: var(--rz-grid-header-filter-icon-margin-inline);
  font-size: var(--rz-grid-header-filter-icon-font-size);
  transition: var(--rz-grid-state-transition);
  font-weight: 400;
  &:hover {
    cursor: pointer;
    color: var(--rz-grid-header-filter-icon-hover-color);
  }
}

.rzi.rz-grid-filter-active {
  color: var(--rz-grid-header-filter-icon-active-color) !important;
  --rz-icon-fill: 1;
}

.rz-button.rz-grid-filter-active {
  color: var(--rz-grid-simple-filter-icon-active-color) !important;
  background-color: var(--rz-grid-simple-filter-icon-active-background-color) !important;
}

.rz-data-grid {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.rz-data-grid-data {
  overflow: auto;
  flex: 1;
  outline: var(--rz-outline-normal);
  box-shadow: var(--rz-grid-data-border-shadow);
}

.rz-grid-table td, .rz-grid-table th {
  padding: var(--rz-grid-cell-padding);
}

.rz-grid-table thead {
  position: sticky;
  top: 0;
  z-index: 2;

  th {
    position: sticky;
    top: 0;
    z-index: 1;
  }
}

.rz-grid-table-fixed {
  table-layout: fixed;

  .rz-frozen-cell {
    position: -webkit-sticky;
    position: sticky;
  }

  .rz-frozen-cell-left, 
  .rz-frozen-cell-right,
  .rz-frozen-cell-left-inner,
  .rz-frozen-cell-right-inner {
    background: var(--rz-grid-frozen-cell-background-color);
    z-index: 1;
  }

  .rz-frozen-cell-left.rz-frozen-cell-left-end {
    box-shadow: 5px 0 5px -5px rgba(0,0,0,0.12);
    border-inline-end: var(--rz-grid-frozen-cell-border) !important;

    *[dir="rtl"] & {
      box-shadow: -5px 0 5px -5px rgba(0,0,0,0.12);
    }
  }

  .rz-frozen-cell-right.rz-frozen-cell-right-end {
    box-shadow: -5px 0 5px -5px rgba(0,0,0,0.12);
    border-inline-start: var(--rz-grid-frozen-cell-border) !important;

    *[dir="rtl"] & {
      box-shadow: 5px 0 5px -5px rgba(0,0,0,0.12);
    }
  }

  .rz-frozen-cell-left-inner,
  .rz-frozen-cell-right-inner {
    border-inline-end: var(--rz-grid-frozen-cell-border) !important;
    border-inline-start: var(--rz-grid-frozen-cell-border) !important;
  }
}

.rz-grid-table tfoot, .rz-grid-table tfoot td {
  position: sticky;
  bottom: 0;
  z-index: 1;
}

.rz-grid-table {
  width: 100%;
  position: relative;
  border-collapse: separate;
  border-spacing: 0;

  th {
    white-space: nowrap;
    overflow: hidden;
  }

  td {
    white-space: nowrap;
    overflow: hidden;
  }
}

.rz-grid-table tbody > div {
  display: table-row;
}

.rz-column-drag {
  @extend %rzi;

  cursor: grab;
  font-size: inherit;
  color: var(--rz-column-drag-handle-color);
  transition: var(--rz-grid-state-transition);
  margin-inline: var(--rz-column-drag-handle-margin-inline);

  &:after {
    content: 'more_vert';
  }

  &:hover {
    color: var(--rz-column-drag-handle-hover-color);
  }

  &:active {
    color: var(--rz-column-drag-handle-hover-color);
    cursor: grabbing;
  }
}

.rz-column-draggable {
  background-color: var(--rz-grid-header-background-color);
  border-radius: var(--rz-border-radius);
  box-shadow: var(--rz-column-draggable-shadow);
  padding: 0;
  display: flex;
  align-items: center;

  > div {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    height: 100%;
    outline: none;
    padding: 0;
  }

  .rz-column-drag {
    max-width: 1rem;
  }

  .rz-column-title {
    display: inline-flex;
    flex: auto;
    font-size: var(--rz-grid-header-font-size);
    font-weight: var(--rz-grid-header-font-weight);
    line-height: var(--rz-grid-header-line-height);
    text-transform: var(--rz-grid-header-text-transform);
    color: var(--rz-grid-header-color);
    padding: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .rz-grid-filter-icon {
    display: none;
  }
}

.rz-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--rz-grid-group-header-gap);
  padding: var(--rz-grid-group-header-padding);
  background-color: var(--rz-grid-header-background-color);
  border-bottom: var(--rz-grid-bottom-cell-border);
}

.rz-group-header-items {
  display: flex;
  flex-wrap: wrap;
  gap: var(--rz-grid-group-header-gap);
}

.rz-group-header-item {
  display: flex;
  align-items: center;
  color: var(--rz-grid-group-header-item-color);
  background-color: var(--rz-grid-group-header-item-background-color);
  border: var(--rz-grid-group-header-item-border);
  border-radius: var(--rz-grid-group-header-item-border-radius);
  padding-block: var(--rz-grid-group-header-item-padding-block);
  padding-inline: var(--rz-grid-group-header-item-padding-inline);
  width: fit-content;
  float: left;

  .rz-dialog-titlebar-close {
    display: flex;
    align-items: center;
    text-decoration: none;
  }
}

.rz-group-header-item-title {
  font-size: var(--rz-grid-header-font-size);
  font-weight: var(--rz-grid-header-font-weight);
  margin-inline: var(--rz-grid-group-header-item-title-margin-inline);
}

.rz-group-header-drop {
  font-size: var(--rz-body-font-size);
  color: var(--rz-text-tertiary-color);
  height: fit-content;
}

.rz-column-picker {
  display: flex;
}

.rz-filter-menu-symbol {
  width: 1.75rem;
  display: inline-block;
}

.rz-filter-button {
  flex: none;
  margin-inline-end: 0.5rem;
}

// Density
.rz-data-grid {
  &.rz-density-compact {
    --rz-grid-cell-line-height: 1rem;
    --rz-grid-cell-padding: 0.25rem 0.5rem;
    --rz-grid-header-cell-padding: 0.25rem 0;
    --rz-grid-header-padding: 0.25rem 1rem;
    --rz-grid-header-title-padding-inline: 0.5rem 0;
    --rz-grid-filter-padding: 0.25rem 0.5rem;
    --rz-grid-group-header-padding: 0.25rem;
    --rz-grid-group-header-gap: 0.25rem;
    --rz-grid-group-header-item-padding-block: 0.125rem ;
    --rz-grid-group-header-item-padding-inline: 0.5rem 0.25rem;
    --rz-grid-group-header-item-title-margin-inline: 0 0.25rem;
    --rz-dialog-close-font-size: 1rem;
  }
}