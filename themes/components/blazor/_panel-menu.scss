$panel-menu-padding-block: 0 !default;
$panel-menu-padding-inline: 0 !default;
$panel-menu-font-size: var(--rz-body-font-size) !default;
$panel-menu-font-weight: 600 !default;
$panel-menu-focus-outline: var(--rz-outline-focus) !default;
$panel-menu-focus-outline-offset: calc(-1 * var(--rz-outline-width)) !default;
$panel-menu-item-line-height: 1.25rem !default;
$panel-menu-item-padding-block: 0.75rem !default;
$panel-menu-item-padding-inline: 1rem !default;
$panel-menu-item-margin-block: 0 !default;
$panel-menu-item-margin-inline: 0 !default;
$panel-menu-item-border: var(--rz-border-base-900) !default;
$panel-menu-item-border-radius: 0 !default;
$panel-menu-item-color: var(--rz-text-contrast-color) !default;
$panel-menu-item-background-color: var(--rz-base-800) !default;
$panel-menu-item-hover-color: $panel-menu-item-color !default;
$panel-menu-item-hover-background-color: rgba($rz-black, .3) !default;
$panel-menu-item-active-color: $panel-menu-item-color !default;
$panel-menu-item-active-background-color: var(--rz-base-900) !default;
$panel-menu-item-active-indicator: var(--rz-secondary) !default;
$panel-menu-item-offset: 0.75rem !default;
$panel-menu-item-transition: color var(--rz-transition), background-color var(--rz-transition) !default;
$panel-menu-2nd-level-vertical-offset: 0 !default;
$panel-menu-item-2nd-level-padding-block: 0.5rem !default;
$panel-menu-item-2nd-level-padding-inline: 1rem !default;
$panel-menu-item-2nd-level-margin-block: 0 !default;
$panel-menu-item-2nd-level-margin-inline: 0 !default;
$panel-menu-item-2nd-level-border-radius: 0 !default;
$panel-menu-item-2nd-level-offset: 2.75rem !default;
$panel-menu-item-2nd-level-font-size: var(--rz-body-font-size) !default;
$panel-menu-item-2nd-level-font-weight: 400 !default;
$panel-menu-item-2nd-level-color: inherit !default;
$panel-menu-item-2nd-level-background-color: var(--rz-base-900) !default;
$panel-menu-item-2nd-level-hover-color: inherit !default;
$panel-menu-item-2nd-level-hover-background-color: rgba($rz-black, .4) !default;
$panel-menu-item-2nd-level-active-color: inherit !default;
$panel-menu-item-2nd-level-active-background-color: $panel-menu-item-2nd-level-hover-background-color !default;
$panel-menu-item-2nd-level-active-font-weight: inherit !default;
$panel-menu-item-3rd-level-color: inherit !default;
$panel-menu-item-3rd-level-background-color: rgba($rz-black, .4) !default;
$panel-menu-item-3rd-level-hover-color: inherit !default;
$panel-menu-item-3rd-level-hover-background-color: rgba($rz-black, .6) !default;
$panel-menu-item-3rd-level-active-color: inherit !default;
$panel-menu-item-3rd-level-active-background-color: $panel-menu-item-3rd-level-hover-background-color !default;
$panel-menu-icon-width: var(--rz-icon-size) !default;
$panel-menu-icon-font-size: var(--rz-icon-size) !default;
$panel-menu-icon-color: inherit !default;
$panel-menu-icon-height: $panel-menu-icon-width !default;
$panel-menu-icon-margin-inline: 0 0.5rem !default;
$panel-menu-icon-2nd-level-margin-inline: -1.625rem 0.625rem !default;
$panel-menu-icon-2nd-level-icon-size: calc(var(--rz-icon-size)*0.8) !default;
$panel-menu-toggle-icon-font-size: var(--rz-icon-size) !default;
$panel-menu-toggle-icon-opacity: 1 !default;

.rz-panel-menu {
  box-sizing: border-box;
  list-style: none;
  padding-block: var(--rz-panel-menu-padding-block);
  padding-inline: var(--rz-panel-menu-padding-inline);
  margin-block-start: 0;
  margin-block-end: 0;
  overflow: auto;
  font-size: var(--rz-panel-menu-font-size);
  font-weight: var(--rz-panel-menu-font-weight);
  color: var(--rz-panel-menu-item-color);
  background-color: var(--rz-panel-menu-item-background-color);

  &:focus {
    outline: var(--rz-outline-normal);
  }

  &:focus-visible {
    .rz-navigation-item {
      &.rz-state-focused {
        > .rz-navigation-item-wrapper {
          outline: var(--rz-panel-menu-focus-outline);
          outline-offset: var(--rz-panel-menu-focus-outline-offset);
        }
      }
    }
  }

  /* First level items */
  & > .rz-navigation-item {
    margin-block: var(--rz-panel-menu-item-margin-block);
    margin-inline: var(--rz-panel-menu-item-margin-inline);
  }

  .rz-navigation-item {
    border-block-end: var(--rz-panel-menu-item-border);
    border-radius: var(--rz-panel-menu-item-border-radius);
    overflow: hidden;

    /* Do not show expand arrow if there are no child items */
    &:not(:has(> .rz-expander .rz-navigation-item)) > .rz-navigation-item-wrapper .rz-navigation-item-icon-children {
      display: none;
    } 
  }

  .rz-navigation-item-wrapper {
    position: relative;
    line-height: var(--rz-panel-menu-item-line-height);
    transition: var(--rz-panel-menu-item-transition);
    border-radius: var(--rz-panel-menu-item-border-radius);
    overflow: hidden;

    &:hover {
      background-color: var(--rz-panel-menu-item-hover-background-color);
      color: var(--rz-panel-menu-item-hover-color);

      .rz-navigation-item-icon {
        color: var(--rz-panel-menu-item-hover-color);
      }
    }
  }
  .rz-navigation-item-active,
  .rz-navigation-item-wrapper-active {
    background-color: var(--rz-panel-menu-item-active-background-color);
    color: var(--rz-panel-menu-item-active-color);

    &:before {
      position: absolute;
      content: '';
      inset-block-start: 0;
      inset-block-end: 0;
      width: 4px;
      background-color: var(--rz-panel-menu-item-active-indicator);
    }

    .rz-navigation-item-icon {
      color: var(--rz-panel-menu-item-active-color);
    }
  }

  .rz-navigation-item-link {
    padding-block: var(--rz-panel-menu-item-padding-block);
    padding-inline: var(--rz-panel-menu-item-padding-inline);
    color: inherit;
    cursor: pointer;
    text-decoration: none;
  }

  a.rz-navigation-item-link {
    cursor: pointer;
    text-decoration: none;
  }

  .rz-navigation-item-text {
    flex: auto;
  }

  .rz-navigation-item-icon-children {
    margin-inline-start: auto;
    font-size: var(--rz-panel-menu-toggle-icon-font-size);
    opacity: var(--rz-panel-menu-toggle-icon-opacity);
    z-index: 1;

    &.rz-state-expanded {
      transform: rotate(180deg);
      transition: transform var(--rz-expander-transition);
    }

    &.rz-state-collapsed {
      transform: rotate(0);
      transition: transform var(--rz-expander-transition);
    }
  }

  .rz-navigation-item-icon {
    height: var(--rz-panel-menu-icon-height);
    width: var(--rz-panel-menu-icon-width);
    color: var(--rz-panel-menu-icon-color);
    margin-inline: var(--rz-panel-menu-icon-margin-inline);
    font-size: var(--rz-panel-menu-icon-font-size);
    transition: var(--rz-panel-menu-item-transition);
  }

  /* Second level items */
  .rz-navigation-menu {
    list-style: none;
    padding: 0;
    margin-block-end: 0;
    overflow: hidden;

    .rz-navigation-item-wrapper {
      margin-block: var(--rz-panel-menu-item-2nd-level-margin-block);
      margin-inline: var(--rz-panel-menu-item-2nd-level-margin-inline);
      border-radius: var(--rz-panel-menu-item-2nd-level-border-radius);
      background-color: var(--rz-panel-menu-item-2nd-level-background-color);
      overflow: hidden;

      &:hover {
        background-color: var(--rz-panel-menu-item-2nd-level-hover-background-color);
        color: var(--rz-panel-menu-item-2nd-level-hover-color);

        .rz-navigation-item-icon {
          color: var(--rz-panel-menu-item-2nd-level-hover-color);
        }
      }
    }

    .rz-navigation-item-active,
    .rz-navigation-item-wrapper-active {
      background-color: var(--rz-panel-menu-item-2nd-level-active-background-color);
      color: var(--rz-panel-menu-item-2nd-level-active-color);
      font-weight: var(--rz-panel-menu-item-2nd-level-active-font-weight);

      .rz-navigation-item-icon {
        color: var(--rz-panel-menu-item-2nd-level-active-color);
      }
    }

    .rz-navigation-item-icon {
      height: var(--rz-panel-menu-icon-2nd-level-icon-size);
      width: var(--rz-panel-menu-icon-2nd-level-icon-size);
      font-size: var(--rz-panel-menu-icon-2nd-level-icon-size);
      margin-inline: var(--rz-panel-menu-icon-2nd-level-margin-inline);
    }

    .rz-navigation-item {
      border-block-end: none;
      color: var(--rz-panel-menu-item-2nd-level-color);
      font-size: var(--rz-panel-menu-item-2nd-level-font-size);
      font-weight: var(--rz-panel-menu-item-2nd-level-font-weight);

      &:first-child {
        margin-block-start: var(--rz-panel-menu-2nd-level-vertical-offset);
      }

      &:last-child {
        margin-block-end: var(--rz-panel-menu-2nd-level-vertical-offset);
      }

      .rz-navigation-item-link {
        padding-block: var(--rz-panel-menu-item-2nd-level-padding-block);
        padding-inline: var(--rz-panel-menu-item-2nd-level-padding-inline);
        padding-inline-start: var(--rz-panel-menu-item-2nd-level-offset);
      }
    }

    /* Third level items */
    .rz-navigation-menu {
      margin: 0;

      .rz-navigation-item-wrapper {
        color: var(--rz-panel-menu-item-3rd-level-color);
        background-color: var(--rz-panel-menu-item-3rd-level-background-color);

        &:hover {
          color: var(--rz-panel-menu-item-3rd-level-hover-color);
          background-color: var(--rz-panel-menu-item-3rd-level-hover-background-color);
        }
      }

      .rz-navigation-item-active,
      .rz-navigation-item-wrapper-active {
        background-color: var(--rz-panel-menu-item-3rd-level-active-background-color);
        color: var(--rz-panel-menu-item-3rd-level-active-color);
      }
    }
  }
}
