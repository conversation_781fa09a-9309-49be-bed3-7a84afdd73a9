$editor-border: var(--rz-input-border) !default;
$editor-border-radius: var(--rz-border-radius) !default;
$editor-toolbar-background-color: var(--rz-base-background-color) !default;
$editor-toolbar-item-margin: 0.25rem 0.125rem !default;
$editor-button-padding: 0.5rem !default;
$editor-button-background-color: var(--rz-base-background-color) !default;
$editor-button-color: var(--rz-text-color) !default;
$editor-button-disabled-color: var(--rz-text-disabled-color) !default;
$editor-button-selected-background-color: var(--rz-secondary) !default;
$editor-button-selected-color: var(--rz-on-secondary) !default;
$editor-separator-background-color: var(--rz-base-200) !default;
$editor-content-background-color: var(--rz-base-background-color) !default;
$editor-focus-outline: var(--rz-outline-focus) !default;
$editor-focus-outline-offset: calc(-1 * var(--rz-outline-width)) !default;

.rz-html-editor {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  border-radius: var(--rz-editor-border-radius);
  border: var(--rz-editor-border);
  overflow: hidden;

  &:focus-within {
    outline: var(--rz-editor-focus-outline);
    outline-offset: var(--rz-editor-focus-outline-offset);
  }
}

.rz-html-editor-content {
  flex: 1;
  overflow: auto;
  padding: 0.5rem;
  outline: none;
  background-color: var(--rz-editor-content-background-color);
}

.rz-html-editor-source.rz-textarea {
    --rz-input-hover-shadow: none;
    --rz-input-border: none;
    --rz-input-hover-border: none;
    --rz-input-focus-shadow: none;
    --rz-input-focus-border: none;
    flex: 1;
    padding: 0.5rem;
    overflow: auto;
    outline: none;
    background-color: var(--rz-editor-content-background-color);
}

.rz-html-editor-toolbar {
  border-bottom: var(--rz-editor-border);
  display: flex;
  line-height: 1rem;
  flex-wrap: wrap;
  background-color: var(--rz-editor-toolbar-background-color);

  .rzi {
    font-size: 1rem;
  }

  > * {
    margin: var(--rz-editor-toolbar-item-margin);
  }
}

.rz-html-editor-colorpicker {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--rz-editor-button-background-color);
  padding: var(--rz-editor-button-padding);

  .rz-colorpicker-trigger {
    color: inherit;
    background-color: var(--rz-editor-button-background-color);

    .rzi {
      font-size: 1.25rem;
      height: 1rem;
    }
  }

  .rz-colorpicker-value {
    display: none;
  }

  .rz-colorpicker {
    border: none;
    box-shadow: none;
    padding: 0;
    height: auto;
    background-color: var(--rz-editor-button-background-color);

    &:not(:disabled):not(.rz-state-disabled):hover {
      border: none;
      box-shadow: none;
    }
  }
}

.rz-html-editor-color {
  border: none;
  display: flex;
  flex-direction: column;
  background: inherit;
  color: inherit;
  appearance: none;
  padding: 0;
  position: relative;

  &:disabled {
    color: var(--rz-input-disabled-color);
  }
}

.rz-html-editor-color-value {
  position: absolute;
  bottom: -4px;
  height: 4px;
  width: 100%;
}

.rz-html-editor-button {
  color: var(--rz-editor-button-color);
  appearance: none;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--rz-editor-button-background-color);
  padding: var(--rz-editor-button-padding);

  &.rz-selected {
    background-color: var(--rz-editor-button-selected-background-color);
    color: var(--rz-editor-button-selected-color);
    border-radius: var(--rz-editor-border-radius);
  }

  &:disabled {
    color: var(--rz-editor-button-disabled-color);
  }
}

.rz-html-editor-dropdown {
  display: inline-flex;
  padding: var(--rz-editor-button-padding);
  align-items: center;
  cursor: pointer;

  &.rz-disabled {
    color: var(--rz-input-disabled-color);
    cursor: default;
  }
}

.rz-html-editor-dropdown-items {
  @extend %dropdown-panel;
}

.rz-html-editor-dropdown-item {
  cursor: default;
  font-size: var(--rz-dropdown-item-font-size);
  padding: var(--rz-dropdown-item-padding);
  white-space: nowrap;

  @include dropdown-item-hover(0);

  &.rz-selected {
    background-color: var(--rz-dropdown-item-selected-background-color);
    color: var(--rz-dropdown-item-selected-color);
  }
}

.rz-html-editor-dropdown-trigger {
  border: none;
  appearance: none;
  padding: 0;
  display: inline-flex;
  align-items: center;
  color: inherit;
  background-color: inherit;

  .rzi {
    &:before {
      content: 'arrow_drop_down';
    }
  }
}

.rz-html-editor-dropdown-items {
  display: none;
}

.rz-html-editor-dialog-item {
  margin-bottom: 1rem;

  label {
    &:first-child {
      display: block;
    }
  }
}

.rz-html-editor-dialog-buttons {
  text-align: right;
}

.rz-html-editor-separator {
  width: 1px;
  background-color: var(--rz-editor-separator-background-color);
}

.rz-html-editor-toolbar {

  .rz-html-editor-colorpicker {
    .rz-colorpicker {
      &:not(:disabled):not(.rz-state-disabled):hover {
        border: none;
      }
    }

    .rz-colorpicker-trigger {
      .rzi {
        font-size: 1.25rem;
        height: 1rem;
      }
    }
  }
}