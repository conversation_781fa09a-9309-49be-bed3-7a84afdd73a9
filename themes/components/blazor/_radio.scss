$radio-width: 1.25rem !default;
$radio-height: $radio-width !default;
$radio-border-radius: 50% !default;
$radio-border-width: inherit !default;
$radio-label-margin-block: 0 !default;
$radio-label-margin-inline: 1rem !default;
$radio-margin-block: 0 !default;
$radio-margin-inline: 0 !default;
$radio-focus-outline: var(--rz-outline-focus) !default;
$radio-focus-outline-offset: var(--rz-outline-offset) !default;
$radio-active-background-color: var(--rz-secondary) !default;
$radio-active-shadow: inset 0 3px 0 0 rgba(0, 0, 0, 0.1) !default;
$radio-checked-background-color: var(--rz-secondary) !default;
$radio-checked-hover-background-color: var(--rz-secondary-light) !default;
$radio-checked-hover-shadow: inset 0 -3px 0 0 rgba(#fff, 0.2) !default;
$radio-checked-color: var(--rz-text-contrast-color) !default;
$radio-circle-background-color: var(--rz-base-background-color) !default;
$radio-circle-shadow: inset 0 4px 7px 0 rgba(0, 0, 0, 0.03) !default;
$radio-circle-hover-background-color: var(--rz-base-100) !default;
$radio-icon-width: 0.5rem !default;
$radio-icon-height: $radio-icon-width !default;
$radio-checked-border: var(--rz-input-border-block-end) !default;


.rz-radio-button-list {
  box-sizing: border-box;
  border-radius: var(--rz-border-radius);

  .rz-radio-btn {
    display: inline-flex;
    align-items: center;
    margin-block: var(--rz-radio-margin-block);
    margin-inline: var(--rz-radio-margin-inline);
    cursor: pointer;
  }

  &:focus {
    outline: var(--rz-outline-normal);
  }

  &:focus-visible {
    outline: var(--rz-radio-focus-outline);
    outline-offset: var(--rz-radio-focus-outline-offset);
  }
}

.rz-radio-button-list-horizontal {
  --rz-gap: 0;
}

.rz-radio-button-list-vertical {
  --rz-gap: 1rem;
}

.rz-state-disabled .rz-radiobutton {
  cursor: initial;
}

.rz-radiobutton-label,
.rz-radiobutton-template {
  margin-block: var(--rz-radio-label-margin-block);
  margin-inline: var(--rz-radio-label-margin-inline);
}

.rz-radiobutton {
  position: relative;
  vertical-align: middle;
  white-space: nowrap;
  cursor: pointer;
  width: var(--rz-radio-width);
  height: var(--rz-radio-height);
  border-radius: var(--rz-radio-border-radius);
}

.rz-radiobutton-box {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: inherit;

  border: var(--rz-input-border-block-end);
  border-width: var(--rz-radio-border-width);
  border-radius: var(--rz-radio-border-radius);
  box-shadow: var(--rz-input-shadow);
  background-color: var(--rz-input-background-color);
  transition: var(--rz-transition-all);

  &:hover:not(.rz-state-disabled) {
    box-shadow: var(--rz-input-hover-shadow);
    background-color: var(--rz-input-hover-background-color);
    border: var(--rz-input-hover-border);
    border-width: var(--rz-radio-border-width);
  }

  &:active:not(.rz-state-disabled) {
    background-color: var(--rz-radio-active-background-color);
    box-shadow: var(--rz-radio-active-shadow);
    border-width: var(--rz-radio-border-width);
  }

  &.rz-state-disabled {
    cursor: initial;
    box-shadow: var(--rz-input-disabled-shadow);
    background-color: var(--rz-input-disabled-background-color);
    border: var(--rz-input-disabled-border);
    border-width: var(--rz-radio-border-width);
    opacity: var(--rz-input-disabled-opacity);
  }

  .rzi {
    width: var(--rz-radio-icon-width);
    height: var(--rz-radio-icon-height);
    color: var(--rz-radio-checked-color);
  }

  .rzi-circle-on {
    border-radius: var(--rz-radio-border-radius);
    vertical-align: middle;
    background-color: var(--rz-radio-circle-background-color);
    box-shadow: var(--rz-radio-circle-shadow);

    &:hover:not(.rz-state-disabled) {
      background-color: var(--rz-radio-circle-hover-background-color);
    }
  }

  &.rz-state-active {
    background-color: var(--rz-radio-checked-background-color);
    border: var(--rz-radio-checked-border);
    border-width: var(--rz-radio-border-width);

    &:hover:not(.rz-state-disabled) {
      background-color: var(--rz-radio-checked-hover-background-color);
      border: var(--rz-radio-checked-border);
      box-shadow: var(--rz-radio-checked-hover-shadow);
      border-width: var(--rz-radio-border-width);
    }

    &.rz-state-disabled {
      background-color: var(--rz-radio-checked-background-color);
      border: var(--rz-radio-checked-border);
      border-width: var(--rz-radio-border-width);
      opacity: 0.5;
    }
  }

  .rz-radio-button-list:focus-visible &.rz-state-focused:not(.rz-state-disabled) { 
    outline: var(--rz-radio-focus-outline);
    outline-offset: var(--rz-radio-focus-outline-offset);
  }
}
