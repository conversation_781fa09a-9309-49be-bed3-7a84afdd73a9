$colorpicker-panel-border: var(--rz-border-normal) !default;
$colorpicker-panel-background-color: var(--rz-base-background-color) !default;
$colorpicker-panel-shadow: 0 6px 14px 0 rgba(0, 0, 0, 0.06) !default;
$colorpicker-panel-padding: 0.5rem !default;
$colorpicker-panel-max-width: 20rem !default;
$colorpicker-saturation-height: 200px !default;
$colorpicker-focus-outline: var(--rz-outline-focus) !default;
$colorpicker-focus-outline-offset: var(--rz-outline-offset) !default;
$colorpicker-value-border-radius: var(--rz-border-radius) !default;
$colorpicker-items-gap: 0.5rem !default;
$colorpicker-item-size: 1.25rem !default;
$colorpicker-item-border-radius: var(--rz-border-radius) !default;
$colorpicker-item-shadow: rgba(0, 0, 0, 0.15) 0px 0px 0px 1px inset, rgba(0, 0, 0, 0.25) 0px 0px 4px inset !default;
$colorpicker-handle-size: 12px !default;
$colorpicker-handle-border: 2px solid var(--rz-white) !default;
$colorpicker-handle-shadow: var(--rz-white) 0px 0px 0px 1px, rgba(0, 0, 0, 0.3) 0px 0px 1px 1px inset, rgba(0, 0, 0, 0.4) 0px 0px 1px 2px !default;
$colorpicker-hex-input-padding: 0.25rem 0.5rem !default;
$colorpicker-hex-input-height: 2rem !default;
$colorpicker-rgba-input-padding: 0.25rem 0.5rem !default;
$colorpicker-rgba-input-height: 2rem !default;
$colorpicker-input-labels-color: var(--rz-text-tertiary-color) !default;

.rz-colorpicker {
  display: inline-flex;
  align-items: center;
  cursor: pointer;

  @extend %input;

  &.rz-state-disabled {
    @extend %input-disabled;
    cursor: default;
  }
}

button.rz-colorpicker-trigger {
  border: none;
  appearance: none;
  padding: 0;
  display: inline-flex;
  align-items: center;
  color: var(--rz-text-color);
  background-color: inherit;
  outline: none;

  .rzi {
    font-size: var(--rz-icon-size);
    &:before {
      content: 'arrow_drop_down';
    }
  }

  .rz-state-disabled & {
    color: var(--rz-input-disabled-color);
  }

}

.rz-colorpicker-popup {
  display: none;
  position: absolute;
  border: var(--rz-colorpicker-panel-border);
  background-color: var(--rz-colorpicker-panel-background-color);
  box-shadow: var(--rz-colorpicker-panel-shadow);
  min-width: 200px;
  max-width: var(--rz-colorpicker-panel-max-width);
  padding: var(--rz-colorpicker-panel-padding);
  border-radius: var(--rz-border-radius);
}

.rz-colorpicker-value {
  flex: 1;
  border-radius: var(--rz-colorpicker-value-border-radius);
  border: var(--rz-colorpicker-panel-border);
  min-width: 20px;
  min-height: 20px;
}

.rz-saturation-picker {
  height: var(--rz-colorpicker-saturation-height);
  position: relative;
  touch-action: none;
  border-radius: var(--rz-border-radius);

  &:focus {
    outline: var(--rz-outline-normal);
  }

  &:focus-visible {
    outline: var(--rz-colorpicker-focus-outline);
    outline-offset: var(--rz-colorpicker-focus-outline-offset);
  }
}

.rz-saturation-white {
  background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0));
  box-shadow: inset 0 0 0 1px rgba(0,0,0,.06);
}

.rz-saturation-black,
.rz-saturation-white {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: var(--rz-border-radius);
  border-start-start-radius: calc(var(--rz-border-radius) - 1px);
}

.rz-saturation-black {
  background: linear-gradient(to top, #000, rgba(0, 0, 0, 0));
}

.rz-saturation-handle {
  position: absolute;
  width: var(--rz-colorpicker-handle-size);
  height: var(--rz-colorpicker-handle-size);
  border: var(--rz-colorpicker-handle-border);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: var(--rz-colorpicker-handle-shadow);
}

.rz-hue-picker {
  margin-bottom: 8px;
  touch-action: none;
  position: relative;
  background-image: linear-gradient(
    to right,
    rgb(255, 0, 0) 0%,
    rgb(255, 255, 0) 17%,
    rgb(0, 255, 0) 33%,
    rgb(0, 255, 255) 50%,
    rgb(0, 0, 255) 67%,
    rgb(255, 0, 255) 83%,
    rgb(255, 0, 0) 100%
  );
  border-radius: var(--rz-border-radius);
  height: var(--rz-colorpicker-handle-size);
  box-shadow: inset 0 0 0 1px rgba(0,0,0,.06);

  &:active {
    cursor: none;
  }

  &:focus {
    outline: var(--rz-outline-normal);
  }

  &:focus-visible {
    outline: var(--rz-colorpicker-focus-outline);
    outline-offset: var(--rz-colorpicker-focus-outline-offset);
  }
}

.rz-alpha-picker {
  touch-action: none;
  position: relative;
  border-radius: var(--rz-border-radius);
  height: var(--rz-colorpicker-handle-size);
  box-shadow: inset 0 0 0 1px rgba(0,0,0,.06);

  &:active {
    cursor: none;
  }

  &:focus {
    outline: var(--rz-outline-normal);
  }

  &:focus-visible {
    outline: var(--rz-colorpicker-focus-outline);
    outline-offset: var(--rz-colorpicker-focus-outline-offset);
  }
}

.rz-hue-handle,
.rz-alpha-handle {
  position: absolute;
  height: 100%;
  width: 8px;
  border: var(--rz-colorpicker-handle-border);
  border-radius: calc(var(--rz-border-radius) / 2);
  transform: translateX(-50%);
  box-shadow: var(--rz-colorpicker-handle-shadow);
}

.rz-colorpicker-preview-area {
  display: flex;
}

.rz-hue-and-alpha {
  flex: 1;
  padding-inline-end: 8px;
}

.rz-alpha-picker,
.rz-colorpicker-preview {
  &:before {
    position: absolute;
    z-index: -1;
    content: '';
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    border-radius: var(--rz-border-radius);
    background-image: linear-gradient(45deg, var(--rz-text-disabled-color) 25%, transparent 25%),
      linear-gradient(-45deg, var(--rz-text-disabled-color) 25%, transparent 25%),
      linear-gradient(45deg, transparent 75%, var(--rz-text-disabled-color) 75%),
      linear-gradient(-45deg, transparent 75%, var(--rz-text-disabled-color) 75%);
    background-size: 8px 8px;
    background-position: 0 0, 0 4px, 4px -4px, -4px 0px;
  }
}

.rz-colorpicker-preview {
  position: relative;
  width: 32px;
  height: 32px;
  box-shadow: rgba(0, 0, 0, 0.15) 0px 0px 0px 1px inset,
    rgba(0, 0, 0, 0.25) 0px 0px 4px inset;
  border-radius: var(--rz-border-radius);
}

.rz-colorpicker-rgba {
  display: flex;
  gap: 4px;
}

.rz-color-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  color: var(--rz-colorpicker-input-labels-color);
  font-size: 0.75rem;
  text-transform: uppercase;

  .rz-textbox {
    width: 80px;
    margin-inline-end: 4px;
    padding: var(--rz-colorpicker-hex-input-padding);
    height: var(--rz-colorpicker-hex-input-height);
  }

  .rz-numeric {
    padding: var(--rz-colorpicker-rgba-input-padding);
    height: var(--rz-colorpicker-rgba-input-height);

    .rz-numeric-input {
      padding: 0;
      outline: none;
    }

    button {
      display: none;
    }
    button:hover {
      display: initial;
    }
  }
}

.rz-colorpicker-button {
  justify-content: flex-end;
  display: flex;
}

.rz-colorpicker-section {
  &:not(:last-child) {
    margin-bottom: 8px;
  }
}

.rz-colorpicker-colors {
  display: flex;
  flex-wrap: wrap;
  gap: var(--rz-colorpicker-items-gap);
}

.rz-colorpicker-item {
  width: var(--rz-colorpicker-item-size);
  height: var(--rz-colorpicker-item-size);
  border-radius: var(--rz-colorpicker-item-border-radius);
  box-shadow: var(--rz-colorpicker-item-shadow);
  cursor: pointer;

  &:focus {
    outline: var(--rz-outline-normal);
  }

  &:focus-visible {
    outline: var(--rz-colorpicker-focus-outline);
    outline-offset: var(--rz-colorpicker-focus-outline-offset);
  }
}
