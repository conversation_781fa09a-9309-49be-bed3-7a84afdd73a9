@import 'variables';
@import 'mixins';
$theme-name: software;

// Theme Colors

$rz-white: #ffffff !default;
$rz-black: #000000 !default;

$rz-base: #dadfe2 !default;
$rz-primary: #598087 !default;
$rz-secondary: #80a4ab !default;
$rz-info: #2cc8c8 !default;
$rz-success: #5dbf74 !default;
$rz-warning: #fac152 !default;
$rz-danger: #f9777f !default;

$rz-series-1: #376df5 !default;
$rz-series-2: #64dfdf !default;
$rz-series-3: #f68769 !default;
$rz-series-4: #c161e2 !default;
$rz-series-5: #fdd07a !default;
$rz-series-6: #f8629b !default;
$rz-series-7: #74d062 !default;
$rz-series-8: #84a7ff !default;
$rz-series-9: #4d99f9 !default;
$rz-series-10: #8cecec !default;
$rz-series-11: #fab793 !default;
$rz-series-12: #da88ee !default;
$rz-series-13: #fee3ab !default;
$rz-series-14: #fb89c3 !default;
$rz-series-15: #a2e389 !default;
$rz-series-16: #b5caff !default;
$rz-series-17: #1750f3 !default;
$rz-series-18: #46d7d7 !default;
$rz-series-19: #f46e4c !default;
$rz-series-20: #b343db !default;
$rz-series-21: #fdc55f !default;
$rz-series-22: #f64485 !default;
$rz-series-23: #58c544 !default;
$rz-series-24: #6a93ff !default;

$rz-base-50: #ffffff !default;
$rz-base-100: #f6f7fa !default;
$rz-base-200: #e9edf0 !default;
$rz-base-300: #dadfe2 !default;
$rz-base-400: #c1c9cb !default;
$rz-base-500: #95a4a8 !default;
$rz-base-600: #77858b !default;
$rz-base-700: #545e61 !default;
$rz-base-800: #3a474d !default;
$rz-base-900: #28363c !default;
$rz-base-light: #e9edf0 !default;
$rz-base-lighter: #ffffff !default;
$rz-base-dark: #77858b !default;
$rz-base-darker: #28363c !default;

$rz-primary-light: mix($rz-white, $rz-primary, 12%)!default;
$rz-primary-lighter: rgba($rz-primary, .16) !default;
$rz-primary-dark: mix($rz-black, $rz-primary, 8%) !default;
$rz-primary-darker: mix($rz-black, $rz-primary, 25%) !default;

$rz-secondary-light: mix($rz-white, $rz-secondary, 12%) !default;
$rz-secondary-lighter: rgba($rz-secondary, .20) !default;
$rz-secondary-dark: mix($rz-black, $rz-secondary, 8%) !default;
$rz-secondary-darker: mix($rz-black, $rz-secondary, 25%) !default;

$rz-info-light: mix($rz-white, $rz-info, 16%) !default;
$rz-info-lighter: rgba($rz-info, .20) !default;
$rz-info-dark: mix($rz-black, $rz-info, 16%) !default;
$rz-info-darker: mix($rz-black, $rz-info, 25%) !default;

$rz-success-light: mix($rz-white, $rz-success, 16%) !default;
$rz-success-lighter: rgba($rz-success, .16) !default;
$rz-success-dark: mix($rz-black, $rz-success, 16%) !default;
$rz-success-darker: mix($rz-black, $rz-success, 25%) !default;

$rz-warning-light: mix($rz-white, $rz-warning, 16%) !default;
$rz-warning-lighter: rgba($rz-warning, .20) !default;
$rz-warning-dark: mix($rz-black, $rz-warning, 16%) !default;
$rz-warning-darker: mix($rz-black, $rz-warning, 25%) !default;

$rz-danger-light: mix($rz-white, $rz-danger, 16%) !default;
$rz-danger-lighter: rgba($rz-danger, .20) !default;
$rz-danger-dark: mix($rz-black, $rz-danger, 16%) !default;
$rz-danger-darker: mix($rz-black, $rz-danger, 25%) !default;

// Theme Constants

$rz-border-width: 1px !default;
$rz-border-radius: 4px !default;
$rz-root-font-size: 16px !default;
$rz-body-font-size: 0.875rem !default;
$rz-body-line-height: 1.429 !default;
$rz-body-background-color: var(--rz-base-100) !default;
$rz-text-font-family: 'Source Sans Pro', -apple-system, BlinkMacSystemFont,
'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji',
'Segoe UI Emoji', 'Segoe UI Symbol' !default;
$rz-outline-offset: 2px !default;
$rz-outline-width: 2px !default;
$rz-outline-color: var(--rz-primary) !default;

// Grid
$grid-simple-filter-icon-active-color: var(--rz-secondary-darker) !default;
$grid-simple-filter-icon-active-background-color: var(--rz-secondary-lighter) !default;

@import 'fonts';
@import 'components';
