@inherits RadzenComponentWithChildren

@if (Visible)
{
    <ul @ref=@Element style=@Style @attributes=@Attributes class=@GetCssClass() id=@GetId()
        tabindex="0" @onkeydown="@OnKeyPress" @onkeydown:preventDefault=preventKeyPress @onkeydown:stopPropagation
        @onfocus=@this.AsNonRenderingEventHandler(OnFocus)>
        @if (Responsive)
        {
        <li class="rz-menu-toggle-item">
            <button aria-label=@ToggleAriaLabel class="rz-menu-toggle" @onclick=@OnToggle>
                @if (IsOpen)
                {
                    <i class="notranslate rzi">close</i>
                } else
                {

                    <i class="notranslate rzi">menu</i>
                }
            </button>
        </li>
        }
        <CascadingValue Value=this>
            @ChildContent
        </CascadingValue>
    </ul>
}
