@typeparam TItem
@if (RowIndex == Column.GetLevel())
{
<td rowspan="@(Column.GetRowSpan())" colspan="@(Column.GetColSpan())" @attributes="@Attributes" class="@CssClass" scope="col" style="@GetStyle()">
    <span class="rz-column-footer">
        @if (Column.FooterTemplate != null)
        {
            @Column.FooterTemplate
        }
    </span>
</td>
}
else
{
    @foreach(var column in Grid.childColumns.Where(c => c.GetVisible() && c.Parent == Column))
    {
        <RadzenDataGridFooterCell RowIndex="@RowIndex" Grid="@Grid" Column="@column" Style="@column.GetStyle(true, true)"
                                CssClass="@($"{Column.FooterCssClass} {Grid.getFrozenColumnClass(column, Grid.ColumnsCollection.Where(c => c.GetVisible()).ToList())} {Grid.getCompositeCellCSSClass(column)}")" 
                                Attributes="@(Attributes)" />
    }
}
@code {
    [Parameter(CaptureUnmatchedValues = true)]
    public IReadOnlyDictionary<string, object> Attributes { get; set; }

    [Parameter]
    public RadzenDataGridColumn<TItem> Column { get; set; }

    [Parameter]
    public int RowIndex { get; set; }

    [Parameter]
    public RadzenDataGrid<TItem> Grid { get; set; }

    [Parameter]
    public string CssClass { get; set; }

    [Parameter]
    public string Style { get; set; }

    private string GetStyle()
    {
        var styles = new List<string>() { Column.GetStyle(true, true), Style };

        if (Attributes?.TryGetValue("style", out var styleAttribute) == true)
        {
            styles.Add(Convert.ToString(styleAttribute));
        }

        var finalStyle = string.Join(";",
                styles
                .Select(x => x?.Trim().TrimEnd(';'))
                .Where(x => !string.IsNullOrEmpty(x))
            );

        return finalStyle;
    }
}