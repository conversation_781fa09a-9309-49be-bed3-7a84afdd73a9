﻿@using Radzen.Blazor
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Forms

@inherits RadzenComponent

@using System.Linq

@if (Steps != null)
{
    <CascadingValue Value=this>
        @Steps
    </CascadingValue>
}
@if (Visible)
{
<div @ref="@Element" style="@Style" @attributes="Attributes" class="@GetCssClass()" id="@GetId()">
    <ul role="tablist">
        @for (var i = 0; i < steps.Count; i++)
        {
            var step = steps[i];
            @if (step.Visible)
            {
                    <li class="@step.GetItemCssClass()" @attributes="step.Attributes" style="@step.Style" tabindex="@(step.Disabled ? -1 : 0)" @onkeypress="@(args => OnKeyPress(args, SelectStep(step, true)))" @onkeypress:preventDefault=preventKeyPress @onkeypress:stopPropagation>
                    <a id="@(GetId() + i.ToString() + "s")" title="@step.Title" aria-label="@step.AriaLabel"
                       @onclick="@(async (args) => { if (!step.Disabled && AllowStepSelect) { await SelectStep(step, true); } })"
                       @onclick:preventDefault="true" class="rz-menuitem-link">
                        <span class="rz-steps-number">@(steps.Where(s => s.Visible).ToList().IndexOf(step) + 1)</span>
                        @if (step.Template != null)
                        {
                            @step.Template(step)
                        }
                        else
                        {
                            <span class="rz-steps-title">@step.Text</span>
                        }
                    </a>
                </li>
            }
        }
    </ul>

    @for (var i = 0; i < steps.Count; i++)
    {
        var step = steps[i];
        @if (step.Visible)
        {
            @if (IsSelected(i, step))
            {
                <div class="rz-widget-content">
                    @if (step.ChildContent != null)
                    {
                        @step.ChildContent
                    }
                </div>
            }
        }
    }
    @if(ShowStepsButtons)
    {
    <div class="rz-steps-buttons">
        <a id="@(GetId() + "prev")" title="@PreviousTitle" aria-label="@PreviousAriaLabel" tabindex="@(IsFirstVisibleStep() ? -1 : 0)"
           class='@($"rz-steps-prev {(IsFirstVisibleStep() ?  "rz-state-disabled" : "")}")'
                   @onkeypress="@(args => OnKeyPress(args, PrevStep()))" @onkeypress:preventDefault=preventKeyPress @onkeypress:stopPropagation
           @onclick="@PrevStep" @onclick:preventDefault="true"><span class="notranslate rzi"></span>@PreviousText</a>
        <a id="@(GetId() + "next")" title="@NextTitle" aria-label="@NextAriaLabel" tabindex="@(IsLastVisibleStep() ? -1 : 0)"
           class='@($"rz-steps-next {(IsLastVisibleStep() ?  "rz-state-disabled" : "")}")' 
                   @onkeypress="@(args => OnKeyPress(args, NextStep()))" @onkeypress:preventDefault=preventKeyPress @onkeypress:stopPropagation
           @onclick="@NextStep" @onclick:preventDefault="true">@NextText<span class="notranslate rzi"></span></a>
    </div>
    }
</div>
}
