<text class=@Class fill=@Fill alignment-baseline="middle" text-anchor=@TextAnchor x=@Position.X.ToInvariantString() y=@Position.Y.ToInvariantString()>@Value</text>
@code {
    [Parameter]
    public string Class { get; set; }

    [Parameter]
    public string Value { get; set; }

    [Parameter]
    public Point Position { get; set; }

    [Parameter]
    public string TextAnchor { get; set; } = "middle";

    [Parameter]
    public string Fill { get; set; }
}