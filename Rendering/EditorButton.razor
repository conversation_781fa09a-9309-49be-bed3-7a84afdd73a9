@if (PreventBlur)
{
<button type="button" tabindex="-1" @onclick=@OnClick class=@Class disabled=@(Editor.Disabled || Disabled || !EnabledModes.HasFlag(Editor.GetMode())) @onmousedown=@Empty @onmousedown:preventDefault @onclick:preventDefault title=@GetTitle()>
    <i class="notranslate rzi" style="@(!string.IsNullOrEmpty(IconColor) ? $"color:{IconColor}" : null)">@Icon</i>
</button>
}
else
{
<button type="button" tabindex="-1" @onclick=@OnClick class=@Class disabled=@(Editor.Disabled || Disabled || !EnabledModes.HasFlag(Editor.GetMode())) title=@GetTitle()>
    <i class="notranslate rzi" style="@(!string.IsNullOrEmpty(IconColor) ? $"color:{IconColor}" : null)">@Icon</i>
</button>
}
@code {
    [CascadingParameter]
    public RadzenHtmlEditor Editor { get; set; }

    [Parameter]
    public string Title { get; set; }

    [Parameter]
    public string Shortcut { get; set; }

    [Parameter]
    public bool Disabled { get; set; }

    [Parameter]
    public bool PreventBlur { get; set; } = true;

    [Parameter]
    public bool Selected { get; set; }

    [Parameter]
    public string Icon { get; set; }

    /// <summary>
    /// Gets or sets the icon color.
    /// </summary>
    /// <value>The icon color.</value>
    [Parameter]
    public string IconColor { get; set; }

    [Parameter]
    public HtmlEditorMode EnabledModes { get; set; } = HtmlEditorMode.Design;

    [Parameter]
    public EventCallback Click { get; set; }

    async Task OnClick()
    {
        await Click.InvokeAsync(null);
    }

    string GetTitle() => !string.IsNullOrEmpty(Shortcut) ? $"{Title} ({Shortcut})" : Title;

    string Class => ClassList.Create("rz-html-editor-button")
        .Add("rz-selected", Selected && !Editor.Disabled && EnabledModes.HasFlag(Editor.GetMode()))
        .ToString();

    void Empty()
    {

    }
}