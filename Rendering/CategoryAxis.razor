@inherits RadzenChartComponentBase

@if (XAxis.Visible)
{
    <g class="rz-axis rz-category-axis">
    <Line class="rz-line" X1="@x1" Y1="@y1" X2="@x2" Y2="@y1" Stroke="@XAxis.Stroke" StrokeWidth="@XAxis.StrokeWidth" LineType="@XAxis.LineType" />
    @for (var idx = start; idx <= end; idx += step)
    {
        var value = Chart.CategoryScale.Value((double)idx);
        var x = Chart.CategoryScale.Scale((double)idx, true);

        var text = XAxis.Format(Chart.CategoryScale, value);

        if (XAxis.Ticks.Template != null)
        {
            var context = new TickTemplateContext { Text = text, Value = value, X = x, Y = y1 } ;

            <CategoryAxisTick X="@x" Y="@y1" Text="@text" Stroke="@(XAxis.Ticks.Stroke ?? XAxis.Stroke)" StrokeWidth="@XAxis.Ticks.StrokeWidth" LineType="@XAxis.Ticks.LineType">
                @XAxis.Ticks.Template(context)
            </CategoryAxisTick>
        }
        else if (!String.IsNullOrEmpty(text))
        {
            <CategoryAxisTick Rotate=@rotate X="@x" Y="@y1" Text="@text" Stroke="@(XAxis.Ticks.Stroke ?? XAxis.Stroke)" StrokeWidth="@XAxis.Ticks.StrokeWidth" LineType="@XAxis.Ticks.LineType"/>
        }

        if (XAxis.GridLines.Visible && idx > start)
        {
            <Line class="rz-grid-line" X1="@x" Y1="@y1" X2="@x" Y2="@y2" Stroke="@XAxis.GridLines.Stroke" StrokeWidth="@XAxis.GridLines.StrokeWidth" LineType="@XAxis.GridLines.LineType" />
        }
    }
    @if (!String.IsNullOrEmpty(XAxis.Title.Text))
    {
        <CategoryAxisTitle X="@((x2 - x1)/2)" Y="@(y1 + Chart.CategoryAxis.Size - XAxis.Title.Size)" Text="@XAxis.Title.Text" />
    }
    </g>
}
@code {
    decimal start;
    decimal end;
    decimal step;
    double x1;
    double x2;
    double y1;
    double y2;

    private AxisBase XAxis { get; set; }
    private AxisBase YAxis { get; set; }

    private double? rotate;

    protected override void OnParametersSet()
    {
        XAxis = Chart.CategoryAxis;
        YAxis = Chart.ValueAxis;

        if (Chart.ShouldInvertAxes())
        {
            XAxis = Chart.ValueAxis;
            YAxis = Chart.CategoryAxis;
        }

        var ticks = Chart.CategoryScale.Ticks(XAxis.TickDistance);
        start = (decimal)ticks.Start;
        end = (decimal)ticks.End;
        step = (decimal)ticks.Step;

        x1 = Chart.CategoryScale.Scale(ticks.Start);
        x2 = Chart.CategoryScale.Scale(ticks.End);

        var valueTicks = Chart.ValueScale.Ticks(YAxis.TickDistance);
        y1 = Chart.ValueScale.Scale(valueTicks.Start);
        y2 = Chart.ValueScale.Scale(valueTicks.End);

        if (XAxis.LabelRotation != null)
        {
            rotate = XAxis.LabelRotation;
        }
        else if (XAxis.LabelAutoRotation != null)
        {
            rotate = null;

            var tickWidth = (x2 - x1) / ((double)end - (double)start);

            for (var idx = start; idx <= end; idx += step)
            {
                var value = Chart.CategoryScale.Value((double)idx);
                var text = XAxis.Format(Chart.CategoryScale, value);

                var textWidth = TextMeasurer.TextWidth(text) - 8;

                if (textWidth > tickWidth)
                {
                    rotate = XAxis.LabelAutoRotation;
                    break;
                }
            }
        }
    }
}
