@using <PERSON><PERSON><PERSON>
@using Ra<PERSON>zen.Blazor
@using Radzen.Blazor.Rendering
@inject DialogService DialogService
@inherits DropableViewBase


<div class="rz-view rz-month-view">
    <div class="rz-view-header">
        @for (var date = StartDate; date <= StartDate.EndOfWeek(); date = date.AddDays(1))
        {
            <div class="rz-slot-header">
                @date.ToString("ddd", Scheduler.Culture)
            </div>
        }
    </div>
    @{
        var days = 0;
    }
    <div @ref=contentView class="rz-view-content" tabindex="0" @onkeydown="@(args => OnKeyPress(args))" @onkeydown:preventDefault="@preventKeyPress" @onkeydown:stopPropagation
         @onfocus=@this.AsNonRenderingEventHandler(OnFocus)>
         @foreach (var week in weeks)
         {
            <div class="rz-week">
                <div class="rz-events">
                    @for (var bandIndex = 0; bandIndex < week.Bands.Length; bandIndex++)
                    {
                        var band = week.Bands[bandIndex];

                        foreach (var info in band)
                        {
                            var dayIndex = info.DayIndex;
                            var day = week.Days[dayIndex];
                            var slotWidth = 100 / 7.0;
                            var left = dayIndex * slotWidth;
                            var length = Math.Max(1, Math.Ceiling(info.Data.End.Subtract(week.Start).TotalDays) - dayIndex);
                            var width = info.Data.End <= week.End ? (length) * slotWidth : (7 - dayIndex) * slotWidth;
                            var top = (bandIndex + 1) * 1.5;
                            var height = 1.5;

                            <Appointment Data=@info.Data Top=@top Left=@left Width=@width Height=@height Click=@OnAppointmentClick
                                CssClass="@(CurrentDate >= day.Start && CurrentDate <= day.Start.AddDays(1) && object.Equals(currentAppointments?.ElementAtOrDefault(currentAppointment), info.Data) ? "rz-state-focused" : "")"
                                DragStart=@OnAppointmentDragStart />
                        }
                    }
                    @for (var dayIndex = 0; dayIndex < week.Days.Length; dayIndex++)
                    {
                        var slot = week.Days[dayIndex];
                        if (slot.Hidden > 0)
                        {
                            var slotWidth = 100 / 7.0;
                            var left = dayIndex * slotWidth;
                            var top = 1.5 * (MaxAppointmentsInSlot + 1);
                            <a class="rz-event-list-btn" style="top: @(top.ToInvariantString())rem; left: @(left.ToInvariantString())%" @onclick=@(args => OnListClick(slot.Start, slot.Appointments))>@String.Format(MoreText, slot.Hidden)</a>
                        }
                    }
                </div>
                <div class="rz-slots">
                @for (var day = 0; day < 7; day ++)
                {
                    var dayOfWeek = StartDate.AddDays(days++);
                    <div @onclick="@(args => OnSlotClick(dayOfWeek))" ondragover="event.preventDefault();" @ondrop=@(args => @OnDrop(dayOfWeek)) @attributes=@Attributes(dayOfWeek)>
                        <div class="rz-slot-title @(dayOfWeek == CurrentDate ? " rz-state-focused" : "")" @onclick=@(args => OnDayClick(dayOfWeek)) @onclick:stopPropagation>
                            @dayOfWeek.Day
                        </div>
                    </div>
                }
                </div>
            </div>
         }
    </div>
</div>

@code {
    class Day
    {
        public AppointmentData[] Appointments { get; set; }
        public DateTime Start { get; set; }
        public DateTime End { get; set; }
        public int Hidden { get; set; }
    }

    class AppointmentRenderInfo
    {
        public AppointmentData Data { get; set; }
        public int DayIndex { get; set; }
    }

    class Week
    {
        public DateTime Start { get; set; }
        public DateTime End { get; set; }
        public IList<AppointmentRenderInfo>[] Bands { get; set; }
        public Day[] Days { get; set; }
    }

    Week CreateWeek(DateTime weekStart, DateTime weekEnd)
    {
        var bands = Enumerable.Range(0, MaxAppointmentsInSlot).Select(_ => new List<AppointmentRenderInfo>()).ToArray();

        var days = new List<Day>();
        var start = weekStart;
        var dayIndex = 0;

        while (start < weekEnd)
        {
            var end = start.AddDays(1);

            var appointments = AppointmentsInSlot(start, end);

            var slot = new Day { Start = start, End = end, Appointments = appointments };
            dayIndex = days.Count;
            days.Add(slot);

            foreach (var appointment in appointments)
            {
                var placed = bands.Any(b => b.Any(a => a.Data.Equals(appointment)));

                if (placed)
                {
                    continue;
                }

                foreach (var band in bands)
                {
                    if (band.Count == 0)
                    {
                        band.Add(new AppointmentRenderInfo { Data = appointment, DayIndex = dayIndex });

                        placed = true;

                        break;
                    }

                    var lastAppointmentInBand = band[^1];
                    var lastStart = days[lastAppointmentInBand.DayIndex].Start;
                    var lastEnd = lastAppointmentInBand.Data.End;

                    // Check if the appointment overlaps with the last appointment in the band
                    if (lastStart < appointment.End && lastEnd > slot.Start)
                    {
                        // Overlap, we cannot place the appointment in this band
                        continue;
                    }

                    if (lastStart == appointment.End && lastEnd == slot.Start)
                    {
                        // Overlap, we cannot place the appointment in this band
                        continue;
                    }

                    // No overlap, we can add the appointment to this band if there is space for it. A band can contain appointments whose total duration is less than one week.
                    var bandDuration = band.Sum(a => a.Data.End.Subtract(days[a.DayIndex].Start).TotalDays);

                    if (bandDuration < 7)
                    {
                        band.Add(new AppointmentRenderInfo { Data = appointment, DayIndex = dayIndex });

                        placed = true;

                        break;
                    }
                    else
                    {
                        // No space in this band, try the next one
                        continue;
                    }
                }

                if (!placed)
                {
                    slot.Hidden++;
                }
            }

            start = end;
        }

        return new Week { Start = weekStart, End = weekEnd, Bands = bands.ToArray(), Days = days.ToArray() };
    }

    void OnFocus()
    {
        if (CurrentDate == default(DateTime)) 
        { 
            CurrentDate = StartDate; 
        }
    }

    [Parameter]
    public DateTime StartDate { get; set; }

    [Parameter]
    public DateTime EndDate { get; set; }

    [Parameter]
    public int MaxAppointmentsInSlot { get; set; }

    [Parameter]
    public string MoreText { get; set; }

    [CascadingParameter]
    public IScheduler Scheduler { get; set; }

    [Parameter]
    public IEnumerable<AppointmentData> Appointments { get; set; }

    IDictionary<string, object> Attributes(DateTime start)
    {
        var attributes = Scheduler.GetSlotAttributes(start, start.AddDays(1), () => AppointmentsInSlot(start, start.AddDays(1)));
        attributes["class"] = ClassList.Create("rz-slot").Add(attributes).ToString();
        attributes["dropzone"] = "move";
        return attributes;
    }

    async Task OnSlotClick(DateTime date)
    {
        await Scheduler.SelectSlot(date, date.AddDays(1), AppointmentsInSlot(date, date.AddDays(1)));
    }

    async Task OnDayClick(DateTime day)
    {
        await Scheduler.SelectDay(day, AppointmentsInSlot(day, day.AddDays(1)));
    }

    ElementReference contentView;

    async Task OnAppointmentClick(AppointmentData data)
    {
        await Scheduler.SelectAppointment(data);
        await contentView.FocusAsync();
    }

    private AppointmentData[] AppointmentsInSlot(DateTime start, DateTime end)
    {
        if (Appointments == null)
        {
            return Array.Empty<AppointmentData>();
        }

        return Appointments.Where(item => Scheduler.IsAppointmentInRange(item, start, end))
                .OrderBy(item => item.Start)
                .ThenBy(item => item.End)
                .ToArray();
    }

    async Task OnListClick(DateTime date, IEnumerable<AppointmentData> appointments)
    {
        bool preventDefault = await Scheduler.SelectMore(date, date.AddDays(1), appointments);

        if (!preventDefault)
        {
            await DialogService.OpenAsync(date.ToString("d", Scheduler.Culture), ds =>
                @<div class="rz-event-list">
                    <CascadingValue Value=@Scheduler>
                        @foreach(var item in appointments)
                        {
                            <Appointment Data=@item Click="OnListEventClick" />
                        }
                    </CascadingValue>
                </div>
            );
        }
    }

    private List<Week> weeks;

    public override async Task SetParametersAsync(ParameterView parameters)
    {
        var didAppointmentsChange = parameters.DidParameterChange(nameof(Appointments), Appointments);
        var didStartDateChange = parameters.DidParameterChange(nameof(StartDate), StartDate);
        var didEndDateChange = parameters.DidParameterChange(nameof(EndDate), EndDate);
        var didMaxAppointmentsInSlotChange = parameters.DidParameterChange(nameof(MaxAppointmentsInSlot), MaxAppointmentsInSlot);

        await base.SetParametersAsync(parameters);

        if (didAppointmentsChange || didStartDateChange || didEndDateChange || didMaxAppointmentsInSlotChange)
        {
            weeks = new List<Week>();

            for (var date = StartDate; date < EndDate; date = date.AddDays(7))
            {
                var week = CreateWeek(date, date.AddDays(7));

                weeks.Add(week);
            }
        }
    }

    async Task OnListEventClick(AppointmentData data)
    {
        DialogService.Close();

        await OnAppointmentClick(data);
    }

    DateTime CurrentDate;
    int currentAppointment = -1;
    IEnumerable<AppointmentData> currentAppointments = Enumerable.Empty<AppointmentData>();
    bool preventKeyPress = false;

    async Task OnKeyPress(KeyboardEventArgs args)
    {
        var key = args.Code != null ? args.Code : args.Key;
        var arrow = key == "ArrowLeft" || key == "ArrowRight" || key == "ArrowUp" || key == "ArrowDown";
        var updown = key == "ArrowUp" || key == "ArrowDown";
        var leftright = key == "ArrowLeft" || key == "ArrowRight";
        var leftup = key == "ArrowLeft" || key == "ArrowUp";

        if (arrow && !currentAppointments.Any())
        {
            var days = leftright ? 1 : 7;
            CurrentDate = CurrentDate.AddDays(leftup ? -days : days);
            currentAppointments = AppointmentsInSlot(CurrentDate, CurrentDate.AddDays(1)).Take(MaxAppointmentsInSlot);

            preventKeyPress = true;
        }
        else if (arrow && currentAppointments.Any())
        {
            if (updown)
            {
                currentAppointment = Math.Clamp(currentAppointment + (key == "ArrowUp" ? -1 : 1), 0, currentAppointments.Count() - 1);
            }
            else
            {
                CurrentDate = CurrentDate.AddDays(key == "ArrowLeft" ? -1 : 1);
                currentAppointments = AppointmentsInSlot(CurrentDate, CurrentDate.AddDays(1)).Take(MaxAppointmentsInSlot);
            }

            preventKeyPress = true;
        }
        else if (key == "Enter")
        {
            await OnSlotClick(CurrentDate);
            await contentView.FocusAsync();

            preventKeyPress = true;
        }
        else if (key == "Space")
        {
            var appointment = currentAppointments.ElementAtOrDefault(currentAppointment);
            if (appointment != null)
            {
                await OnAppointmentClick(appointment);
            }

            preventKeyPress = true;
        }
        else
        {
            preventKeyPress = false;
        }
    }
}