<div class="rz-slot-hours">
@{var minor = false; }
@for (var date = DateTime.Today.Add(Start); date < DateTime.Today.Add(End); date = date.AddMinutes(MinutesPerSlot))
{
    @if (minor)
    {
        <div class="rz-slot-header"></div>
    }
    else
    {
        <div class="rz-slot-header">@date.ToString(TimeFormat)</div>
    }

    minor = !minor;
}
</div>
@code {
    [Parameter]
    public TimeSpan Start { get; set; }

    [Parameter]
    public TimeSpan End { get; set; }

    [Parameter]
    public string TimeFormat { get; set; } = "h tt";

    [Parameter]
    public int MinutesPerSlot { get; set; }
}