@using Radzen.Blazor
@using Radzen.Blazor.Rendering

@inherits SchedulerYearViewBase

@code {
    public override RenderFragment Render()
    {
        var appointments = Scheduler.GetAppointmentsInRange(StartDate, EndDate);

        var maxAppointmentsInSlot = 0;

        return @<CascadingValue Value=@Scheduler>
                <YearView StartDate=@StartDate EndDate=@EndDate StartMonth=@StartMonth MaxAppointmentsInSlot=@maxAppointmentsInSlot MoreText=@MoreText
                          NoDayEventsText=@NoDayEventsText Appointments=@appointments />
                </CascadingValue>;
    }
}