﻿@using Radzen.Blazor.Rendering
@using System.Linq
@using System.Collections
@using Radzen
@using Microsoft.AspNetCore.Components.Forms
@typeparam TValue
@inherits FormComponent<TValue>

@if (Items != null)
{
    <CascadingValue Value=this>
        @Items
    </CascadingValue>
}
@if (Visible)
{
    <div @ref="@Element" style="@Style" @attributes="Attributes" class="@GetCssClass()" id="@GetId()"
         role="radiogroup" @onfocus=@OnFocus @onblur=@OnBlur
        tabindex="@(Disabled ? "-1" : $"{TabIndex}")" @onkeydown="@(args => OnKeyPress(args))" @onkeydown:preventDefault=preventKeyPress @onkeydown:stopPropagation>
        <RadzenStack Orientation="@Orientation" JustifyContent="@JustifyContent" AlignItems="@AlignItems" Gap="@Gap" Wrap="@Wrap">
        @foreach (var item in allItems.Where(i => i.Visible))
        {
            <div @ref="@item.Element" id="@item.GetItemId()" @onclick="@(args => SelectItem(item))" @attributes="item.Attributes" class="@item.GetItemCssClass()" style="@item.Style" role="radio" aria-checked=@(IsSelected(item)? "true" : "false") aria-label="@item.Text">
                <div class="rz-radiobutton">
                    <div class="rz-helper-hidden-accessible">
                        <input type="radio" disabled="@Disabled" name="@Name" value="@item.Value" tabindex="-1" aria-label="@(item.Text + " " + item.Value)" @attributes="item.InputAttributes">
                    </div>
                    <div class=@ItemClass(item)>
                        <span class=@IconClass(item)></span>
                    </div>
                </div>
                @if (item.Template != null)
                {
                    <div class="rz-radiobutton-template" @onkeydown:stopPropagation>
                        @item.Template(item)
                    </div>
                } 
                else 
                {
                    <span class="rz-radiobutton-label">@item.Text</span>
                }
            </div>
        }
        </RadzenStack>
    </div>
}
