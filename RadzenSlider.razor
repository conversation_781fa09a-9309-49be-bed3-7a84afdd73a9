﻿@using <PERSON><PERSON><PERSON>
@using System.Collections
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.JSInterop
@using Radzen.Blazor.Rendering;

@typeparam TValue
@inherits FormComponent<TValue>
@if (Visible)
{
    <div @ref="Element" style="@Style" @attributes="Attributes" class="@GetCssClass()" id="@GetId()">
        @if (Range)
        {
            <span class="rz-slider-range" style="@(Orientation == Orientation.Horizontal ? "inset-inline-start" : "top"): @((Orientation == Orientation.Horizontal ? Left : 100 - SecondLeft).ToInvariantString())%; @(Orientation == Orientation.Horizontal ? "width" : "height"): @((SecondLeft - Left).ToInvariantString())%;"></span>
            <span tabindex="@(Disabled ? -1 : 0)" @ref="minHandle" class="rz-slider-handle @($"rz-slider-handle-{Orientation.ToString().ToLowerInvariant()}")" style="@(Orientation == Orientation.Horizontal ? "inset-inline-start" : "top"): @((Orientation == Orientation.Horizontal ? Left : 100 - Left).ToInvariantString())%; bottom: auto;" @onkeydown="@(args => OnKeyPress(args, true))" @onkeydown:preventDefault=preventKeyPress @onkeydown:stopPropagation></span>
            <span tabindex="@(Disabled ? -1 : 0)" @ref="maxHandle" class="rz-slider-handle @($"rz-slider-handle-{Orientation.ToString().ToLowerInvariant()}") rz-slider-handle-active" style="@(Orientation == Orientation.Horizontal ? "inset-inline-start" : "top"): @((Orientation == Orientation.Horizontal ? SecondLeft : 100 - SecondLeft).ToInvariantString())%; bottom: auto;" @onkeydown="@(args => OnKeyPress(args, false))" @onkeydown:preventDefault=preventKeyPress @onkeydown:stopPropagation></span>
        }
        else
        {
            <span class="rz-slider-range rz-slider-range-min" style="@(Orientation == Orientation.Horizontal ? "width" : "height"): @(Left.ToInvariantString())%;@(Orientation == Orientation.Horizontal ? "" : $"top:{(100 - Left).ToInvariantString()}%")"></span>
            <span tabindex="@(Disabled ? -1 : 0)" @ref="handle" class="rz-slider-handle @($"rz-slider-handle-{Orientation.ToString().ToLowerInvariant()}")" style="@(Orientation == Orientation.Horizontal ? "inset-inline-start" : "top"): @((Orientation == Orientation.Horizontal ? Left : 100 - Left).ToInvariantString())%;" @onkeydown="@(args => OnKeyPress(args, false))" @onkeydown:preventDefault=preventKeyPress @onkeydown:stopPropagation></span>
        }
    </div>
}
