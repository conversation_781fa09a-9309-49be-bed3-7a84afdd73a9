@using Radzen.Blazor.Rendering

<EditorDropDown Title=@Title Value=@Editor.State.FontName Change=@OnChange Placeholder="@Placeholder"
    PopupStyle="width: 200px; max-height: 200px; overflow: auto;">
    <CascadingValue Value=@this>
        @if (ChildContent != null)
        {
            @ChildContent
        }
        else
        {
            <RadzenHtmlEditorFontNameItem Text="Arial" Value="Arial" />
            <RadzenHtmlEditorFontNameItem Text="Georgia" Value="Georgia" />
            <RadzenHtmlEditorFontNameItem Text="Helvetica" Value="Helvetica" />
            <RadzenHtmlEditorFontNameItem Text="Monospace" Value="monospace" />
            <RadzenHtmlEditorFontNameItem Text="Segoe UI" Value='"Segoe UI"' />
            <RadzenHtmlEditorFontNameItem Text="Tahoma" Value="Tahoma" />
            <RadzenHtmlEditorFontNameItem Text="Times New Roman" Value='"Times New Roman"' />
            <RadzenHtmlEditorFontNameItem Text="Verdana" Value="Verdana" />
        }
    </CascadingValue>
</EditorDropDown>