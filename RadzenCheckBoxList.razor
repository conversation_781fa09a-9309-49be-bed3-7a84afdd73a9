﻿@using System.Linq
@using System.Collections
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor.Rendering
@using Microsoft.AspNetCore.Components.Forms
@typeparam TValue
@inherits FormComponent<IEnumerable<TValue>>

@if (Items != null)
{
    <CascadingValue Value=this>
        @Items
    </CascadingValue>
}
@if (Visible)
{
    <div @ref="@Element" style="@Style" @attributes="Attributes" class="@GetCssClass()" id="@GetId()"
         role="checkboxgroup" @onfocus=@OnFocus @onblur=@OnBlur
        tabindex="@(Disabled ? "-1" : $"{TabIndex}")" @onkeydown="@(args => OnKeyPress(args))" @onkeydown:preventDefault=preventKeyPress @onkeydown:stopPropagation>
        @if (AllowSelectAll)
        {
            <div class="rz-multiselect-header rz-helper-clearfix" @onclick:preventDefault>
                <RadzenCheckBox ReadOnly="@ReadOnly" Disabled="@Disabled" Name="SelectAll" TValue="bool?" Value="@IsAllSelected()" Change="@SelectAll"
                                InputAttributes="@(new Dictionary<string,object>(){ { "aria-label", SelectAllText }})" />
                <RadzenLabel Component="SelectAll" Text="@SelectAllText" class="rz-chkbox-label" />
            </div>
        }
        <RadzenStack Orientation="@Orientation" JustifyContent="@JustifyContent" AlignItems="@AlignItems" Gap="@Gap" Wrap="@Wrap">
        @foreach (var item in allItems.Where(i => i.Visible))
        {
            <div @ref="@item.Element" id="@item.GetItemId()" @onclick="@(args => SelectItem(item))" @attributes="item.Attributes" class="@item.GetItemCssClass()" style="@item.Style" role="checkbox" aria-checked=@(IsSelected(item)? "true" : "false") aria-label="@item.Text">
                <div class="rz-chkbox">
                    <div class="rz-helper-hidden-accessible">
                        <input type="checkbox" name="@Name" value="@item.Value" disabled="@Disabled" readonly="@ReadOnly" tabindex="-1" aria-label="@(item.Text + " " + item.Value)" aria-checked="@(IsSelected(item).ToString().ToLowerInvariant())">
                    </div>
                    <div class=@ItemClass(item)>
                        <span class=@IconClass(item)></span>
                    </div>
                </div>
                @if (item.Template != null)
                {
                    <div class="rz-chkbox-template" @onkeydown:stopPropagation>
                        @item.Template(item)
                    </div>
                }
                else
                {
                    <label class="rz-chkbox-label" aria-hidden="true">@item.Text</label>
                }
            </div>
        }
        </RadzenStack>
    </div>
}
