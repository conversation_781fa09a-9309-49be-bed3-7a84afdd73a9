﻿@using <PERSON><PERSON><PERSON>
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components.Forms
@using System.Globalization
@typeparam TValue
@inherits FormComponentWithAutoComplete<TValue>

@if (Visible)
{
    <span @ref="@Element" style="@Style" @attributes="Attributes" class="@GetCssClass()" id="@GetId()">
        @RenderInput()
        @if (ShowUpDown)
        {
            <button aria-label=@UpAriaLabel type="button" class="rz-numeric-button rz-numeric-up rz-button" tabindex="-1"
                    @onclick="@((args) => UpdateValueWithStep(true))">
                <span class="notranslate rz-numeric-button-icon rzi rzi-caret-up"></span>
            </button>
            <button aria-label=@DownAriaLabel type="button" class="rz-numeric-button rz-numeric-down rz-button" tabindex="-1"
                    @onclick="@((args) => UpdateValueWithStep(false))">
                <span class="notranslate rz-numeric-button-icon rzi rzi-caret-down"></span>
            </button>
        }
    </span>
}
@code {
    internal RenderFragment RenderInput()
    {
#if NET7_0_OR_GREATER
        return __builder => {
            <text>
                <input @ref="@input" inputmode="decimal" @attributes="InputAttributes" type="text" name="@Name" disabled="@Disabled" readonly="@ReadOnly"
                       class="@GetInputCssClass()" tabindex="@(Disabled ? "-1" : $"{TabIndex}")" id="@Name"
                       placeholder="@CurrentPlaceholder" autocomplete="@AutoCompleteAttribute" aria-autocomplete="@AriaAutoCompleteAttribute" @bind:get="FormattedValue" @bind:set="SetValue"
                       onkeypress="Radzen.numericKeyPress(event, @IsInteger().ToString().ToLower(), '@Culture.NumberFormat.NumberDecimalSeparator')"
                       onblur="@getOnInput()" onpaste="@getOnPaste()" maxlength="@MaxLength"
                       @onkeydown="@(args => OnKeyPress(args))" @onkeydown:preventDefault=preventKeyPress @onkeydown:stopPropagation />
            </text>
        };
#else
        return __builder => {
            <text>
                <input @ref="@input" inputmode="decimal" @attributes="InputAttributes" type="text" name="@Name" disabled="@Disabled" readonly="@ReadOnly"
                       class="@GetInputCssClass()" tabindex="@(Disabled ? "-1" : $"{TabIndex}")" id="@Name"
                       placeholder="@CurrentPlaceholder" autocomplete="@AutoCompleteAttribute" aria-autocomplete="@AriaAutoCompleteAttribute" value="@FormattedValue" @onchange="@OnChange"
                       onkeypress="Radzen.numericKeyPress(event, @IsInteger().ToString().ToLower(), '@Culture.NumberFormat.NumberDecimalSeparator')"
                       onblur="@getOnInput()" onpaste="@getOnPaste()" maxlength="@MaxLength"
                       @onkeydown="@(args => OnKeyPress(args))" @onkeydown:preventDefault=preventKeyPress @onkeydown:stopPropagation />
            </text>
        };
#endif
    }
}