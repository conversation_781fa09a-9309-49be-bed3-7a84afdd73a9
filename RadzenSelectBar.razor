﻿@using Radzen.Blazor.Rendering
@using System.Linq
@using System.Collections
@typeparam TValue
@using Microsoft.AspNetCore.Components.Forms
@inherits FormComponent<TValue>
@implements IRadzenSelectBar
@if (Items != null)
{
    <CascadingValue Value=this>
        @Items
    </CascadingValue>
}
@if (Visible)
{
    <div @ref="@Element" style="@Style" @attributes="Attributes" class="@GetCssClass()" id="@GetId()"
         role="toolbar" aria-orientation="horizontal" @onfocus=@this.AsNonRenderingEventHandler(OnFocus) @onblur=@this.AsNonRenderingEventHandler(OnBlur)
         tabindex="@(Disabled ? "-1" : $"{TabIndex}")" @onkeydown="@(args => OnKeyPress(args))" @onkeydown:preventDefault=preventKeyPress @onkeydown:stopPropagation>
        @foreach (var item in allItems.Where(i => i.Visible))
        {
            <div @ref="@item.Element" id="@item.GetItemId()"
                @onclick="@(args => SelectItem(item))"
                 @attributes="item.Attributes" style="@item.Style" class=@ButtonClass(item) aria-label="@item.Text" aria-selected=@(IsSelected(item)? "true" : "false")>
                @if (item.Template != null)
                {
                    @item.Template(item)
                }
                else
                {
                    @if (!string.IsNullOrEmpty(item.Icon))
                    {
                        <i class="notranslate rzi rz-navigation-item-icon" style="margin-right:2px;@(!string.IsNullOrEmpty(item.IconColor) ? $"color:{item.IconColor}" : "")">@item.Icon</i>
                    }
                    @if (!string.IsNullOrEmpty(item.Image))
                    {
                        <img class="rz-navigation-item-icon" src="@item.Image" style="@item.ImageStyle" alt=@item.ImageAlternateText/>
                    }
                    <span class="rz-button-text">@item.Text</span>
                }
            </div>
        }
    </div>
}
