﻿@using <PERSON><PERSON><PERSON>
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.JSInterop
@typeparam TValue
@{var itemArgs = ListBox?.ItemAttributes(this); }
@if (itemArgs.Visible)
{
Disabled = itemArgs.Disabled;
<li class="@GetComponentCssClass()" aria-label="@PropertyAccess.GetItemOrValueFromProperty(Item, ListBox.TextProperty)" @onclick="@SelectItem"
        @attributes="@(itemArgs.Attributes.Any() ? itemArgs.Attributes : Attributes)">
    @if (ListBox.Multiple)
    {
        <div class="rz-chkbox ">
            <div class="@(ListBox.IsSelected(Item) ? "rz-chkbox-box rz-state-active" : "rz-chkbox-box") @(Disabled ? " rz-state-disabled  " : "")">
                <span class="@(ListBox.IsSelected(Item) ? "notranslate rz-chkbox-icon rzi rzi-check" : "notranslate rz-chkbox-icon ")"></span>
            </div>
        </div>
    }
    <span class="rz-multiselect-item-content">
        @if (ListBox.Template != null)
        {
            @ListBox.Template(Item)
        }
        else
        {
            @PropertyAccess.GetItemOrValueFromProperty(Item, ListBox.TextProperty)
        }
    </span>
</li>
}
@code {
    [Parameter(CaptureUnmatchedValues = true)]
    public IReadOnlyDictionary<string, object> Attributes { get; set; }

    [Parameter]
    public RadzenListBox<TValue> ListBox { get; set; }

    [Parameter]
    public object Item { get; set; }

    [Parameter]
    public bool Disabled { get; set; }

    async Task SelectItem(MouseEventArgs args)
    {
        if (!ListBox.Disabled && !Disabled)
        { 
            await ListBox.SelectItemInternal(Item); 
        }
    }

    string GetComponentCssClass()
    {
        string result = $"rz-multiselect-item ";
        if (Disabled)
            result += "rz-state-disabled ";
        else if (ListBox.IsSelected(Item))
            result += "rz-state-highlight ";

        return result;
    }
}