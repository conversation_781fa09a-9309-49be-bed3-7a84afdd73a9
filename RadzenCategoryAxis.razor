@using Radzen.Blazor
@using Radzen.Blazor.Rendering
@inherits AxisBase
<CascadingValue Value="@this">
  @ChildContent
</CascadingValue>
@code {
  internal double Measure(RadzenChart chart)
      {
          if (Visible == false)
          {
              return 0;
          }

          if (chart.ShouldInvertAxes())
          {
              return AxisMeasurer.XAxis(chart.ValueScale, chart.ValueAxis, chart.ValueAxis.Title);
          }
          else
          {
              return AxisMeasurer.XAxis(chart.CategoryScale, this, Title);
          }
      }

      [Parameter]
      public double Padding { get; set; }

      internal override double Size
      {
          get
          {
              return Measure(Chart);
          }
      }

      protected override void Initialize()
      {
          Chart.CategoryAxis = this;
      }
}
