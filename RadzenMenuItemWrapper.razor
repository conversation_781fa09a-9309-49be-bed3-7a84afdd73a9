@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.JSInterop
@inherits RadzenComponentWithChildren

@if (Item.Parent.ClickToOpen)
{
    <li style=@Style @attributes="@Attributes" @onclick="@Item.OnClick" @onclick:stopPropagation>
        @ChildContent
    </li>
}
else
{
    if (Item.ChildContent != null)
    {
        <li style=@Style @attributes="@Attributes" onmouseenter="Radzen.toggleMenuItem(this, event, true, false)" onmouseleave="Radzen.toggleMenuItem(this, event, false, false)">
            @ChildContent
        </li>
    }
    else
    {
        <li style=@Style @attributes="@Attributes" @onclick="@Item.OnClick" @onclick:stopPropagation>
            @ChildContent
        </li>
    }
}

@code {

    /// <summary>
    /// Gets or sets the menu item.
    /// </summary>
    /// <value>The menu item.</value>
    [Parameter]
    public RadzenMenuItem Item { get; set; }
}