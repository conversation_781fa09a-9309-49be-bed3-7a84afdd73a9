@using System.Text
@using Radzen.Blazor.Rendering
@using Microsoft.JSInterop
@inherits RadzenHtmlEditorButtonBase
@inject DialogService DialogService

<EditorButton Click=@OnClick Icon="insert_photo" Title=@Title />

@code {
    protected override async Task OnClick()
    {
        await Editor.SaveSelectionAsync();

        var uploadHeaders = Editor.UploadHeaders ?? new Dictionary<string, string>();

        Attributes = await Editor.GetSelectionAttributes<ImageAttributes>("img", new[] {"src", "alt", "width", "height"});

        var result = await DialogService.OpenAsync(Title, ds => @<div class="rz-html-editor-dialog">
            <RadzenTemplateForm TItem="ImageAttributes" Data="@Attributes" Submit="OnSubmit">
                <div class="rz-html-editor-dialog-item">
                    <label>@SelectText</label>
                @if (Editor.UploadUrl != null)
                {
                    <RadzenUpload ChooseText=@UploadChooseText @ref=@FileUpload Url=@Editor.UploadUrl Auto="false" Accept="image/*"
                        style="width: 100%" Complete="OnUploadComplete" Error="OnUploadError">
                        @foreach (var header in uploadHeaders)
                        {
                            <RadzenUploadHeader Name=@header.Key Value=@header.Value />
                        }
                    </RadzenUpload>
                } else {
                    <RadzenFileInput Accept="image/*" ChooseText=@UploadChooseText @bind-Value=@Attributes.Src style="width: 100%" />
                }
                    </div>
                    @if (ShowWidth)
                    {
                        <div class="rz-html-editor-dialog-item">
                            <label>@WidthText</label>
                            <RadzenTextBox @bind-Value=@Attributes.Width style="width: 100%" />
                        </div>
                    }
                    @if (ShowHeight)
                    {
                        <div class="rz-html-editor-dialog-item">
                            <label>@HeightText</label>
                            <RadzenTextBox @bind-Value=@Attributes.Height style="width: 100%" />
                        </div>
                    }
                    @if (ShowSrc)
                    {
                        <div class="rz-html-editor-dialog-item">
                            <label>@UrlText</label>
                            <RadzenTextBox @bind-Value=@Attributes.Src style="width: 100%" />
                        </div>
                    }
                    @if (ShowAlt)
                    {
                        <div class="rz-html-editor-dialog-item">
                            <label>@AltText</label>
                            <RadzenTextBox @bind-Value=@Attributes.Alt style="width: 100%" />
                        </div>
                    }
                    <div class="rz-html-editor-dialog-buttons">
                        <RadzenButton Text=@OkText ButtonType="ButtonType.Submit" />
                        <RadzenButton Text=@CancelText Click="()=> ds.Close(false)" ButtonStyle="ButtonStyle.Secondary" />
                    </div>
                </RadzenTemplateForm>
            </div>
        );
    }
}
