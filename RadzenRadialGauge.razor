@using Radzen.Blazor
@using Radzen.Blazor.Rendering
@inherits GaugeBase
@if (Visible)
{
    <div @ref="Element" style=@Style @attributes=@Attributes class=@GetCssClass() id=@GetId()>
    @if (Width.HasValue && Height.HasValue)
    {
        <svg style="width: 100%; height: 100%">
            <CascadingValue Value=@this>
                @ChildContent
            </CascadingValue>
        </svg>
        @if (Pointers.Any())
        {
            @foreach (var pointer in Pointers)
            {
                <div class="rz-gauge-value" style=@PointerStyle(pointer)>
                    @if (pointer.ShowValue)
                    {
                        @if (pointer.Template != null)
                        {
                            @pointer.Template(pointer)
                        }
                        else if (!string.IsNullOrEmpty(pointer.FormatString))
                        {
                            @string.Format(pointer.FormatString, pointer.Value)
                        }
                        else
                        {
                            @pointer.Value
                        }
                    }
                </div>
            }
        }
    }
    </div>
}

@code {
    IList<RadzenRadialGaugeScalePointer> Pointers { get; set; } = new List<RadzenRadialGaugeScalePointer>();

    string PointerStyle(RadzenRadialGaugeScalePointer pointer)
    {
        return $"left: {pointer.Scale.CurrentCenter.X.ToInvariantString()}px; top: {pointer.Scale.CurrentCenter.Y.ToInvariantString()}px";
    }

    internal void AddPointer(RadzenRadialGaugeScalePointer pointer)
    {
        if (!Pointers.Contains(pointer))
        {
            Pointers.Add(pointer);

            StateHasChanged();
        }
    }

    protected override string GetComponentCssClass()
    {
        return $"rz-gauge";
    }
}