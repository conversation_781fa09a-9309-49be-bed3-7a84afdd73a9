﻿@inherits RadzenComponentWithChildren
@if (visible)
{
    <div @ref="@Element" style="@Style" @attributes="Attributes" class="@GetCssClass()" id="@GetId()" aria-live="polite">
        <div class="rz-alert-item">
            @if (ShowIcon)
            {
               <RadzenIcon Icon="@GetIcon()" IconColor="@IconColor" class="rz-alert-icon" />
            }
            <div class="rz-alert-message">
                @if (!string.IsNullOrEmpty(Title))
                {
                <div class="rz-alert-title">@Title</div>
                }
                <div class="rz-alert-content">
                    @if (ChildContent != null)
                    {
                        @ChildContent
                    }
                    else
                    {
                        @Text
                    }
                </div>
            </div>
        </div>
        @if (AllowClose)
        {
            <RadzenButton Click=@OnClose Icon="close" Variant="Variant.Text" ButtonStyle="@GetCloseButtonStyle()" Shade="@GetCloseButtonShade()" Size="@GetCloseButtonSize()" />
        }
    </div>
}