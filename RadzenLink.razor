﻿@using Microsoft.AspNetCore.Components.Routing
@inherits RadzenComponent
@if (Visible)
{
    <NavLink style="@Style" href="@GetPath()" @attributes="Attributes" class="@GetCssClass()" target="@GetTarget()" id="@GetId()" Match="@Match" >
        @if (!string.IsNullOrEmpty(Icon))
        {
            <i class="notranslate rzi" style="@(!string.IsNullOrEmpty(IconColor) ? $"color:{IconColor}" : null)">@Icon</i>
        }
        @if (!string.IsNullOrEmpty(Image))
        {
            <img class="notranslate rzi" src="@Image" alt="@ImageAlternateText" />
        }
        <span @ref="@Element" class="rz-link-text">@if (ChildContent != null) {@ChildContent} else {@Text}</span>
    </NavLink>
}
