@using Radzen.Blazor
@using Radzen.Blazor.Rendering
@typeparam TItem
@inherits Radzen.Blazor.CartesianSeries<TItem>
<CascadingValue Value="@this">
  @ChildContent
</CascadingValue>
@code {
  public override RenderFragment Render(ScaleBase categoryScale, ScaleBase valueScale)
  {
    var category = ComposeCategory(categoryScale);

    var value = ComposeValue(valueScale);

    var pathGenerator = GetPathGenerator();

    var ticks = Chart.CategoryScale.Ticks(Chart.CategoryAxis.TickDistance);

    var index = Chart.Series.IndexOf(this);

    var topPoints = GetTopPoints(category, value, valueScale);
    var bottomPoints = GetBottomPoints(category, value, valueScale);

    var className = $"rz-area-series rz-series-{index}";

    return
    @<g class="@className">
      @if (Items.Any())
      {
        var x1 = category(Items.First()).ToInvariantString();
        var x2 = category(Items.Last()).ToInvariantString();
        var valueTicks = Chart.ValueScale.Ticks(Chart.ValueAxis.TickDistance);
        var start = Math.Max(0, valueTicks.Start);
        var y = Chart.ValueScale.Scale(start).ToInvariantString();
        var style = $"clip-path: url(#{Chart.ClipPath}); -webkit-clip-path: url(#{Chart.ClipPath});";
        var topPath = pathGenerator.Path(topPoints);
        var bottomPath = pathGenerator.Path(bottomPoints);
        var area = $"M {x1} {y} {topPath} L {x2} {y} {bottomPath}";
        var line = $"M {topPath}";

        <path @key="@area" style="@style" d="@area" fill="@Fill" stroke="none"></path>
        <Path @key="@line" D="@line" Stroke="@Stroke" StrokeWidth="@StrokeWidth" LineType="@LineType" Style="@style" Fill="none" />
      }
      <Markers Visible="@Markers.Visible" Series="@this" Data="@topPoints" MarkerType="@MarkerType" Stroke="@Markers.Stroke" Fill="@(Markers.Fill ?? Stroke)" StrokeWidth="@Markers.StrokeWidth" Size="@Markers.Size" />
    </g>;
  }
}
